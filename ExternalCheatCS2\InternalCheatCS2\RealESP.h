#pragma once
#include "InternalMemory.h"
#include <vector>
#include <windows.h>

struct PlayerInfo;
struct BoundingBox;

class RealESP {
private:
    InternalMemory* m_pMemory;
    bool m_enabled;
    
    void DrawBox(HDC hdc, const BoundingBox& bbox, COLORREF color, int thickness = 2);
    void DrawHealthBar(HDC hdc, const BoundingBox& bbox, int health, int maxHealth = 100);
    void DrawPlayerInfo(HDC hdc, const BoundingBox& bbox, const PlayerInfo& player);
    void RenderPlayer(const PlayerInfo& player);
    
public:
    RealESP(InternalMemory* memory);
    ~RealESP();
    
    void Render(const std::vector<PlayerInfo>& players);
    void SetEnabled(bool enabled);
    bool IsEnabled() const;
};
