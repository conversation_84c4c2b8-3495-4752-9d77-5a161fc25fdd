#pragma once
#include "Config.h"
#include <windows.h>
#include <iostream>

// GUI simples baseada em console para configuração
class GUI {
private:
    bool isInitialized;
    bool showMainWindow;

public:
    GUI();
    ~GUI();

    bool Initialize();
    void Shutdown();
    void Render();
    void HandleInput();

    bool IsVisible() const { return showMainWindow; }
    void SetVisible(bool visible) { showMainWindow = visible; }
    void Toggle() { showMainWindow = !showMainWindow; }

private:
    void ShowConsoleMenu();
    void HandleMenuInput();
    void ClearConsole();
};
