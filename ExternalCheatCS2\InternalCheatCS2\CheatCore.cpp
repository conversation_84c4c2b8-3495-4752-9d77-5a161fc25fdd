#include "pch.h"
#include "CheatCore.h"

CheatCore::CheatCore() 
    : m_bRunning(false), m_bMenuVisible(false), m_bInsertPressed(false), m_bDeletePressed(false) {
}

CheatCore::~CheatCore() {
    Shutdown();
}

bool CheatCore::Initialize() {
    try {
        std::cout << "[CORE] Inicializando componentes..." << std::endl;
        
        // Inicializar configuração
        m_pConfig = std::make_unique<Config>();
        m_pConfig->LoadDefaults();
        
        // Inicializar memória interna
        m_pMemory = std::make_unique<InternalMemory>();
        if (!m_pMemory->Initialize()) {
            std::cout << "[ERRO] Falha ao inicializar memória interna!" << std::endl;
            return false;
        }
        
        // Inicializar DirectX Hook
        m_pDirectXHook = std::make_unique<DirectXHook>();
        if (!m_pDirectXHook->Initialize()) {
            std::cout << "[AVISO] DirectX Hook falhou - ESP funcionará sem overlay" << std::endl;
            // Continuar mesmo se DirectX falhar
        }

        // CORREÇÃO: Desabilitar InternalESP para evitar conflitos com RealESP
        // m_pESP = std::make_unique<InternalESP>(m_pMemory.get(), m_pDirectXHook.get());
        // if (!m_pESP->Initialize()) {
        //     std::cout << "[ERRO] Falha ao inicializar ESP!" << std::endl;
        //     return false;
        // }
        std::cout << "[CORE] InternalESP desabilitado - usando apenas RealESP" << std::endl;

        // Inicializar RealESP (novo ESP profissional)
        m_pRealESP = std::make_unique<RealESP>(m_pMemory.get());
        std::cout << "[CORE] RealESP inicializado!" << std::endl;
        
        m_bRunning = true;
        std::cout << "[CORE] Inicialização concluída!" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[ERRO] Exceção durante inicialização: " << e.what() << std::endl;
        return false;
    }
}

void CheatCore::Update() {
    if (!m_bRunning) return;

    try {
        // Processar input com proteção
        HandleInput();

        // FORÇAR RENDERIZAÇÃO SEMPRE - SEM CONDIÇÕES
        static auto lastUpdate = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() > 50) { // 20 FPS - Renderização suave

            // Debug de estado
            static int debugCounter = 0;
            debugCounter++;

            if (debugCounter % 30 == 0) {
                std::cout << "[CORE DEBUG] RealESP ptr=" << (m_pRealESP ? "OK" : "NULL")
                          << " Config=" << (m_pConfig ? "OK" : "NULL")
                          << " Enabled=" << (m_pConfig ? m_pConfig->esp.enabled : false) << std::endl;
            }

            // CORREÇÃO: Usar APENAS RealESP para evitar conflitos de renderização
            if (m_pRealESP && m_pRealESP->IsEnabled() && m_pMemory) {
                if (debugCounter % 30 == 0) {
                    std::cout << "[CORE] Executando RealESP..." << std::endl;
                }

                // Obter dados dos players diretamente da memória
                auto players = m_pMemory->GetPlayers();

                // Renderizar APENAS com RealESP (sem conflitos)
                m_pRealESP->Render(players);

                if (debugCounter % 30 == 0) {
                    std::cout << "[CORE] RealESP executado com " << players.size() << " players!" << std::endl;
                }
            } else {
                if (debugCounter % 30 == 0) {
                    std::cout << "[CORE ERROR] RealESP ou Memory pointer é NULL!" << std::endl;
                }
            }

            lastUpdate = now;
        }

    } catch (const std::exception& e) {
        std::cout << "[ERRO] Exceção no update: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "[ERRO] Exceção desconhecida no update!" << std::endl;
    }
}

void CheatCore::Shutdown() {
    std::cout << "[CORE] Finalizando..." << std::endl;
    
    m_bRunning = false;
    
    // InternalESP completamente removido
    
    if (m_pDirectXHook) {
        m_pDirectXHook->Shutdown();
        m_pDirectXHook.reset();
    }
    
    if (m_pMemory) {
        m_pMemory->Shutdown();
        m_pMemory.reset();
    }
    
    if (m_pConfig) {
        m_pConfig.reset();
    }
    
    std::cout << "[CORE] Finalização concluída!" << std::endl;
}

void CheatCore::HandleInput() {
    // Toggle menu com INSERT
    bool insertCurrently = GetAsyncKeyState(VK_INSERT) & 0x8000;
    if (insertCurrently && !m_bInsertPressed) {
        ToggleMenu();
        std::cout << "[INPUT] Menu " << (m_bMenuVisible ? "ABERTO" : "FECHADO") << std::endl;
    }
    m_bInsertPressed = insertCurrently;
    
    // Eject com DELETE
    bool deleteCurrently = GetAsyncKeyState(VK_DELETE) & 0x8000;
    if (deleteCurrently && !m_bDeletePressed) {
        std::cout << "[INPUT] Ejetando cheat..." << std::endl;
        m_bRunning = false;
    }
    m_bDeletePressed = deleteCurrently;
    
    // Toggle ESP com F1
    static bool f1Pressed = false;
    bool f1Currently = GetAsyncKeyState(VK_F1) & 0x8000;
    if (f1Currently && !f1Pressed) {
        m_pConfig->esp.enabled = !m_pConfig->esp.enabled;
        std::cout << "[INPUT] ESP " << (m_pConfig->esp.enabled ? "ATIVADO" : "DESATIVADO") << std::endl;
    }
    f1Pressed = f1Currently;
}
