#pragma once

#include <Windows.h>
#include <TlHelp32.h>
#include <string>
#include <vector>
#include <iostream>
#include <cmath>

struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vector3 operator+(const Vector3& other) const {
        return Vector3(x + other.x, y + other.y, z + other.z);
    }
    
    Vector3 operator-(const Vector3& other) const {
        return Vector3(x - other.x, y - other.y, z - other.z);
    }
    
    float Distance(const Vector3& other) const {
        float dx = x - other.x;
        float dy = y - other.y;
        float dz = z - other.z;
        return sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    float Length() const {
        return sqrt(x * x + y * y + z * z);
    }
};

struct Vector2 {
    float x, y;
    
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
};

struct ViewMatrix {
    float matrix[4][4];
};

struct EntityInfo {
    uintptr_t address;
    uintptr_t controller;
    uintptr_t pawn;
    uintptr_t sceneNode;
    Vector3 position;
    Vector3 headPosition;
    int health;
    int armor;
    int team;
    bool alive;
    bool dormant;
    bool spotted;
    std::string name;
    float distance;
    Vector2 screenPos;
    Vector2 headScreenPos;
    bool onScreen;
};

class Memory {
private:
    HANDLE processHandle;
    DWORD processId;
    uintptr_t clientBase;
    uintptr_t engine2Base;
    
    DWORD GetProcessId(const std::wstring& processName);
    uintptr_t GetModuleBase(DWORD processId, const std::wstring& moduleName);
    
public:
    Memory();
    ~Memory();
    
    bool Initialize();
    void Shutdown();
    
    // Leitura de memória
    template<typename T>
    T Read(uintptr_t address) {
        T value = {};
        ReadProcessMemory(processHandle, (LPCVOID)address, &value, sizeof(T), nullptr);
        return value;
    }
    
    // Leitura de string
    std::string ReadString(uintptr_t address, size_t maxLength = 256);
    
    // Escrita de memória
    template<typename T>
    bool Write(uintptr_t address, const T& value) {
        return WriteProcessMemory(processHandle, (LPVOID)address, &value, sizeof(T), nullptr);
    }
    
    // Getters
    uintptr_t GetClientBase() const { return clientBase; }
    uintptr_t GetEngine2Base() const { return engine2Base; }
    HANDLE GetProcessHandle() const { return processHandle; }
    DWORD GetProcessId() const { return processId; }
    
    // Funções específicas do CS2
    uintptr_t GetLocalPlayer();
    uintptr_t GetLocalPlayerController();
    uintptr_t GetEntityList();
    ViewMatrix GetViewMatrix();
    Vector2 GetScreenSize();
    
    // Funções de entidade
    std::vector<EntityInfo> GetPlayers();
    EntityInfo GetEntityInfo(uintptr_t entityAddress, int index);
    bool IsValidEntity(uintptr_t entityAddress);
    
    // World to screen
    bool WorldToScreen(const Vector3& worldPos, Vector2& screenPos, const ViewMatrix& viewMatrix, const Vector2& screenSize);
};
