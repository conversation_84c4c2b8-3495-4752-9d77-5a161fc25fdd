#pragma once
#include "pch.h"
#include "DirectXHook.h"
#include "InternalMemory.h"
#include "InternalESP.h"
#include "RealESP.h"
#include "Config.h"

class CheatCore {
private:
    std::atomic<bool> m_bRunning;
    std::unique_ptr<DirectXHook> m_pDirectXHook;
    std::unique_ptr<InternalMemory> m_pMemory;
    // std::unique_ptr<InternalESP> m_pESP; // REMOVIDO - usando apenas RealESP
    std::unique_ptr<RealESP> m_pRealESP;
    std::unique_ptr<Config> m_pConfig;
    
    // Input handling
    bool m_bMenuVisible;
    bool m_bInsertPressed;
    bool m_bDeletePressed;
    
public:
    CheatCore();
    ~CheatCore();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    bool IsRunning() const { return m_bRunning; }
    void SetRunning(bool running) { m_bRunning = running; }
    
    // Getters
    DirectXHook* GetDirectXHook() const { return m_pDirectXHook.get(); }
    InternalMemory* GetMemory() const { return m_pMemory.get(); }
    // InternalESP* GetESP() const { return m_pESP.get(); } // REMOVIDO
    Config* GetConfig() const { return m_pConfig.get(); }
    
    // Menu
    bool IsMenuVisible() const { return m_bMenuVisible; }
    void ToggleMenu() { m_bMenuVisible = !m_bMenuVisible; }
    
private:
    void HandleInput();
};
