#pragma once

// Windows Headers
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <d3dcompiler.h>

// Standard Library
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <unordered_map>

// Math
#include <cmath>
#include <algorithm>

// DirectX Libraries
#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "dxgi.lib")
#pragma comment(lib, "d3dcompiler.lib")

// Estruturas matemáticas
struct Vector2 {
    float x, y;
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
    
    Vector2 operator+(const Vector2& other) const { return Vector2(x + other.x, y + other.y); }
    Vector2 operator-(const Vector2& other) const { return Vector2(x - other.x, y - other.y); }
    Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
};

struct Vector3 {
    float x, y, z;
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vector3 operator+(const Vector3& other) const { return Vector3(x + other.x, y + other.y, z + other.z); }
    Vector3 operator-(const Vector3& other) const { return Vector3(x - other.x, y - other.y, z - other.z); }
    Vector3 operator*(float scalar) const { return Vector3(x * scalar, y * scalar, z * scalar); }
    
    float Length() const { return sqrt(x * x + y * y + z * z); }
    Vector3 Normalize() const { 
        float len = Length(); 
        return len > 0 ? Vector3(x / len, y / len, z / len) : Vector3(); 
    }
};

struct Matrix4x4 {
    float m[4][4];
    Matrix4x4() { memset(m, 0, sizeof(m)); }
};

// Estrutura de informações do player
struct PlayerInfo {
    uintptr_t entity;
    Vector3 position;
    Vector3 headPosition;
    Vector2 screenPos;
    Vector2 headScreenPos;
    int health;
    int team;
    bool valid;
    bool onScreen;
    float distance;
    std::string name;
    
    PlayerInfo() : entity(0), health(0), team(0), valid(false), onScreen(false), distance(0) {}
};

// Cores
struct Color {
    float r, g, b, a;
    Color() : r(1.0f), g(1.0f), b(1.0f), a(1.0f) {}
    Color(float r, float g, float b, float a = 1.0f) : r(r), g(g), b(b), a(a) {}
    
    static Color Red() { return Color(1.0f, 0.0f, 0.0f); }
    static Color Green() { return Color(0.0f, 1.0f, 0.0f); }
    static Color Blue() { return Color(0.0f, 0.0f, 1.0f); }
    static Color White() { return Color(1.0f, 1.0f, 1.0f); }
    static Color Black() { return Color(0.0f, 0.0f, 0.0f); }
    static Color Yellow() { return Color(1.0f, 1.0f, 0.0f); }
};
