@echo off
echo ========================================
echo    CS2 INTERNAL CHEAT - BUILD SCRIPT
echo ========================================
echo.

echo [1/3] Compilando cheat interno...
cd "..\ExternalCheatCS2"
msbuild InternalCheatCS2.sln /p:Configuration=Release /p:Platform=x64 /m /nologo

if %ERRORLEVEL% NEQ 0 (
    echo [ERRO] Falha na compilacao!
    pause
    exit /b 1
)

echo [2/3] Copiando DLL...
copy "x64\Release\InternalCheatCS2.dll" "..\cs2_internal_cheat\InternalCheatCS2.dll" >nul

if %ERRORLEVEL% NEQ 0 (
    echo [ERRO] Falha ao copiar DLL!
    pause
    exit /b 1
)

echo [3/3] Verificando arquivo...
cd "..\cs2_internal_cheat"
if exist "InternalCheatCS2.dll" (
    echo [SUCESSO] DLL compilada e copiada com sucesso!
    dir InternalCheatCS2.dll
) else (
    echo [ERRO] DLL nao encontrada!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    PRONTO PARA INJECAO!
echo ========================================
echo.
echo Arquivo: InternalCheatCS2.dll
echo Local: cs2_internal_cheat\
echo.
echo Pressione qualquer tecla para continuar...
pause >nul
