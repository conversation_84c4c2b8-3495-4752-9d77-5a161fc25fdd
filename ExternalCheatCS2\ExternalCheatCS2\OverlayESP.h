#pragma once

#include "Memory.h"
#include "Config.h"
#include <Windows.h>
#include <vector>
#include <string>

class OverlayESP {
private:
    Memory* memory;
    
    // <PERSON><PERSON> overlay
    HWND overlayWindow;
    HWND targetWindow;
    HDC overlayDC;
    HBITMAP overlayBitmap;
    HDC memoryDC;
    
    // Configurações da tela
    int screenWidth;
    int screenHeight;
    
    // Cores
    COLORREF enemyColor;
    COLORREF teamColor;
    COLORREF textColor;
    
    // Configurações ESP
    bool enabled;
    bool showBoxes;
    bool showNames;
    bool showHealth;
    bool showDistance;
    bool teamCheck;
    
    // Funções privadas
    bool CreateOverlayWindow();
    bool InitializeGraphics();
    void CleanupGraphics();
    HWND FindCS2Window();
    void UpdateWindowPosition();
    
    // Funções de desenho
    void ClearOverlay();
    void DrawBox(int x, int y, int width, int height, COLORREF color);
    void DrawText(int x, int y, const std::string& text, COLORREF color);
    void DrawLine(int x1, int y1, int x2, int y2, COLORREF color);
    
    // Window procedure
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
public:
    OverlayESP(Memory* mem);
    ~OverlayESP();
    
    bool Initialize();
    void Shutdown();
    void Update();
    void Render();
    
    // Configurações
    void SetEnabled(bool enable) { enabled = enable; }
    bool IsEnabled() const { return enabled; }
    
    void SetShowBoxes(bool show) { showBoxes = show; }
    void SetShowNames(bool show) { showNames = show; }
    void SetShowHealth(bool show) { showHealth = show; }
    void SetShowDistance(bool show) { showDistance = show; }
    void SetTeamCheck(bool check) { teamCheck = check; }
};
