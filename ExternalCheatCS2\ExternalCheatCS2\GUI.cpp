#include "GUI.h"
#include <conio.h>

GUI::GUI() : isInitialized(false), showMainWindow(false) {
}

GUI::~GUI() {
    Shutdown();
}

bool GUI::Initialize() {
    isInitialized = true;
    std::cout << "[INFO] GUI Console inicializada!" << std::endl;
    return true;
}

void GUI::Shutdown() {
    isInitialized = false;
}

void GUI::Render() {
    if (!isInitialized || !showMainWindow) return;

    ShowConsoleMenu();
}

void GUI::HandleInput() {
    if (GetAsyncKeyState(g_Config.menuKey) & 1) {
        Toggle();
    }

    if (GetAsyncKeyState(g_Config.panicKey) & 1) {
        g_Config.panicMode = true;
        showMainWindow = false;
    }

    if (showMainWindow) {
        HandleMenuInput();
    }
}

void GUI::ShowConsoleMenu() {
    ClearConsole();
    std::cout << "========================================" << std::endl;
    std::cout << "       CS2 EXTERNAL CHEAT - CONFIG     " << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    std::cout << "ESP Status: " << (g_Config.esp.enabled ? "ATIVO" : "INATIVO") << std::endl;
    std::cout << std::endl;
    std::cout << "Configurações ESP:" << std::endl;
    std::cout << "1. Toggle ESP (Atual: " << (g_Config.esp.enabled ? "ON" : "OFF") << ")" << std::endl;
    std::cout << "2. Toggle Boxes (Atual: " << (g_Config.esp.showBoxes ? "ON" : "OFF") << ")" << std::endl;
    std::cout << "3. Toggle Nomes (Atual: " << (g_Config.esp.showNames ? "ON" : "OFF") << ")" << std::endl;
    std::cout << "4. Toggle Vida (Atual: " << (g_Config.esp.showHealth ? "ON" : "OFF") << ")" << std::endl;
    std::cout << "5. Toggle Distância (Atual: " << (g_Config.esp.showDistance ? "ON" : "OFF") << ")" << std::endl;
    std::cout << "6. Toggle Aliados (Atual: " << (g_Config.esp.showTeammates ? "ON" : "OFF") << ")" << std::endl;
    std::cout << std::endl;
    std::cout << "7. Distância Máxima: " << g_Config.esp.maxDistance << "m" << std::endl;
    std::cout << "8. FPS Target: " << g_Config.esp.updateRate << std::endl;
    std::cout << std::endl;
    std::cout << "0. Fechar Menu" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Escolha uma opção: ";
}

void GUI::HandleMenuInput() {
    if (_kbhit()) {
        char key = _getch();

        switch (key) {
        case '1':
            g_Config.esp.enabled = !g_Config.esp.enabled;
            std::cout << "\nESP " << (g_Config.esp.enabled ? "ATIVADO" : "DESATIVADO") << std::endl;
            Sleep(1000);
            break;
        case '2':
            g_Config.esp.showBoxes = !g_Config.esp.showBoxes;
            std::cout << "\nBoxes " << (g_Config.esp.showBoxes ? "ATIVADAS" : "DESATIVADAS") << std::endl;
            Sleep(1000);
            break;
        case '3':
            g_Config.esp.showNames = !g_Config.esp.showNames;
            std::cout << "\nNomes " << (g_Config.esp.showNames ? "ATIVADOS" : "DESATIVADOS") << std::endl;
            Sleep(1000);
            break;
        case '4':
            g_Config.esp.showHealth = !g_Config.esp.showHealth;
            std::cout << "\nVida " << (g_Config.esp.showHealth ? "ATIVADA" : "DESATIVADA") << std::endl;
            Sleep(1000);
            break;
        case '5':
            g_Config.esp.showDistance = !g_Config.esp.showDistance;
            std::cout << "\nDistância " << (g_Config.esp.showDistance ? "ATIVADA" : "DESATIVADA") << std::endl;
            Sleep(1000);
            break;
        case '6':
            g_Config.esp.showTeammates = !g_Config.esp.showTeammates;
            std::cout << "\nAliados " << (g_Config.esp.showTeammates ? "ATIVADOS" : "DESATIVADOS") << std::endl;
            Sleep(1000);
            break;
        case '7':
            std::cout << "\nNova distância máxima (100-2000): ";
            int newDist;
            std::cin >> newDist;
            if (newDist >= 100 && newDist <= 2000) {
                g_Config.esp.maxDistance = (float)newDist;
                std::cout << "Distância alterada para " << newDist << "m" << std::endl;
            }
            Sleep(1000);
            break;
        case '8':
            std::cout << "\nNovo FPS target (30-144): ";
            int newFPS;
            std::cin >> newFPS;
            if (newFPS >= 30 && newFPS <= 144) {
                g_Config.esp.updateRate = newFPS;
                std::cout << "FPS alterado para " << newFPS << std::endl;
            }
            Sleep(1000);
            break;
        case '0':
            showMainWindow = false;
            std::cout << "\nMenu fechado!" << std::endl;
            Sleep(500);
            break;
        }
    }
}

void GUI::ClearConsole() {
    system("cls");
}


