#pragma once

#include <cstddef>

// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-11 21:26:46.855887200 UTC

namespace Offsets {
    // Module: client.dll
    namespace Client {
        constexpr std::ptrdiff_t dwCSGOInput = 0x1A77110;
        constexpr std::ptrdiff_t dwEntityList = 0x18652B0;
        constexpr std::ptrdiff_t dwGameEntitySystem = 0x1B27C08;
        constexpr std::ptrdiff_t dwGameEntitySystem_highestEntityIndex = 0x20F0;
        constexpr std::ptrdiff_t dwGameRules = 0x1A68B48;
        constexpr std::ptrdiff_t dwGlobalVars = 0x184BEB0;
        constexpr std::ptrdiff_t dwGlowManager = 0x1A68298;
        constexpr std::ptrdiff_t dwLocalPlayerController = 0x1A52D20;
        constexpr std::ptrdiff_t dwLocalPlayerPawn = 0x18580D0;
        constexpr std::ptrdiff_t dwPlantedC4 = 0x1A71C40;
        constexpr std::ptrdiff_t dwPrediction = 0x1857F50;
        constexpr std::ptrdiff_t dwSensitivity = 0x1A69868;
        constexpr std::ptrdiff_t dwSensitivity_sensitivity = 0x40;
        constexpr std::ptrdiff_t dwViewAngles = 0x1A774E0;
        constexpr std::ptrdiff_t dwViewMatrix = 0x1A6D280;
        constexpr std::ptrdiff_t dwViewRender = 0x1A6DB90;
        constexpr std::ptrdiff_t dwWeaponC4 = 0x1A06570;
    }
    
    // Module: engine2.dll
    namespace Engine2 {
        constexpr std::ptrdiff_t dwBuildNumber = 0x540BE4;
        constexpr std::ptrdiff_t dwNetworkGameClient = 0x53FCE0;
        constexpr std::ptrdiff_t dwNetworkGameClient_clientTickCount = 0x368;
        constexpr std::ptrdiff_t dwNetworkGameClient_deltaTick = 0x27C;
        constexpr std::ptrdiff_t dwNetworkGameClient_isBackgroundMap = 0x281447;
        constexpr std::ptrdiff_t dwNetworkGameClient_localPlayer = 0xF0;
        constexpr std::ptrdiff_t dwNetworkGameClient_maxClients = 0x238;
        constexpr std::ptrdiff_t dwNetworkGameClient_serverTickCount = 0x36C;
        constexpr std::ptrdiff_t dwNetworkGameClient_signOnState = 0x228;
        constexpr std::ptrdiff_t dwWindowHeight = 0x62359C;
        constexpr std::ptrdiff_t dwWindowWidth = 0x623598;
    }
}

// Netvars para entidades
namespace Netvars {
    // C_BaseEntity
    namespace BaseEntity {
        constexpr std::ptrdiff_t m_iHealth = 0x344;
        constexpr std::ptrdiff_t m_iTeamNum = 0x3E3;
        constexpr std::ptrdiff_t m_vecOrigin = 0x88; // CGameSceneNode
        constexpr std::ptrdiff_t m_lifeState = 0x348;
        constexpr std::ptrdiff_t m_hOwnerEntity = 0x440;
        constexpr std::ptrdiff_t m_fFlags = 0x3EC;
        constexpr std::ptrdiff_t m_pGameSceneNode = 0x328;
    }
    
    // C_CSPlayerPawn
    namespace CSPlayerPawn {
        constexpr std::ptrdiff_t m_hPlayerPawn = 0x824; // From controller
        constexpr std::ptrdiff_t m_sSanitizedPlayerName = 0x770; // From controller
        constexpr std::ptrdiff_t m_iPawnHealth = 0x830; // From controller
        constexpr std::ptrdiff_t m_iPawnArmor = 0x834; // From controller
        constexpr std::ptrdiff_t m_bPawnIsAlive = 0x82C; // From controller
        constexpr std::ptrdiff_t m_angEyeAngles = 0x1438;
        constexpr std::ptrdiff_t m_vecViewOffset = 0xCB8;
        constexpr std::ptrdiff_t m_hActiveWeapon = 0x1318;
        constexpr std::ptrdiff_t m_flFlashDuration = 0x140C;
        constexpr std::ptrdiff_t m_bSpotted = 0x11A4;
        constexpr std::ptrdiff_t m_entitySpottedState = 0x11A8;
        constexpr std::ptrdiff_t m_bSpottedByMask = 0x11AC;
    }
    
    // CGameSceneNode
    namespace GameSceneNode {
        constexpr std::ptrdiff_t m_vecOrigin = 0x88;
        constexpr std::ptrdiff_t m_vecAbsOrigin = 0xD0;
        constexpr std::ptrdiff_t m_angRotation = 0xC0;
        constexpr std::ptrdiff_t m_angAbsRotation = 0xDC;
        constexpr std::ptrdiff_t m_flScale = 0xCC;
        constexpr std::ptrdiff_t m_flAbsScale = 0xE8;
        constexpr std::ptrdiff_t m_bDormant = 0xEF;
    }
    
    // C_BasePlayerController
    namespace BasePlayerController {
        constexpr std::ptrdiff_t m_sSanitizedPlayerName = 0x770;
        constexpr std::ptrdiff_t m_hPlayerPawn = 0x824;
        constexpr std::ptrdiff_t m_bPawnIsAlive = 0x82C;
        constexpr std::ptrdiff_t m_iPawnHealth = 0x830;
        constexpr std::ptrdiff_t m_iPawnArmor = 0x834;
    }
}

// Constantes úteis
namespace Constants {
    constexpr int MAX_ENTITIES = 2048;
    constexpr int MAX_PLAYERS = 64;
    constexpr float PLAYER_HEIGHT = 72.0f;
    constexpr float PLAYER_WIDTH = 32.0f;
    
    // Teams
    constexpr int TEAM_NONE = 0;
    constexpr int TEAM_SPECTATOR = 1;
    constexpr int TEAM_TERRORIST = 2;
    constexpr int TEAM_COUNTER_TERRORIST = 3;
    
    // Life states
    constexpr int LIFE_ALIVE = 0;
    constexpr int LIFE_DYING = 1;
    constexpr int LIFE_DEAD = 2;
}
