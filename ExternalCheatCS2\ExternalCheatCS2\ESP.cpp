#include "ESP.h"
#include "Offsets.h"
#include "../imgui/imgui.h"
#include <algorithm>
#include <cmath>
#include <cstring>

// Funções auxiliares para min/max
template<typename T>
T Min(T a, T b) { return (a < b) ? a : b; }

template<typename T>
T Max(T a, T b) { return (a > b) ? a : b; }

ESP::ESP(Memory* mem) : memory(mem) {
    Vector2 screenSize = memory->GetScreenSize();
    screenCenter = Vector2(screenSize.x / 2.0f, screenSize.y / 2.0f);
}

ESP::~ESP() {
}

void ESP::Update() {
    if (!settings.enabled || !memory) {
        return;
    }
    
    // Atualizar lista de players
    players = memory->GetPlayers();
    
    // Atualizar centro da tela
    Vector2 screenSize = memory->GetScreenSize();
    screenCenter = Vector2(screenSize.x / 2.0f, screenSize.y / 2.0f);
}

void ESP::Render() {
    // ESP EXTERNO DESABILITADO - USAR APENAS CHEAT INTERNO
    return;
    
    // Obter team do local player
    int localTeam = Constants::TEAM_NONE;
    uintptr_t localPlayer = memory->GetLocalPlayer();
    if (localPlayer) {
        localTeam = memory->Read<int>(localPlayer + Netvars::BaseEntity::m_iTeamNum);
    }
    
    // Renderizar cada player
    for (const auto& player : players) {
        if (!ShouldDrawPlayer(player, localTeam)) {
            continue;
        }
        
        // Calcular bounding box
        BoundingBox box = CalculateBoundingBox(player);
        if (!box.valid) {
            continue;
        }
        
        // Obter cor do player
        float color[4];
        GetPlayerColor(player, localTeam, color);
        
        // Renderizar elementos do ESP
        if (settings.boxes) {
            DrawCornerBox(box, color);
        }
        
        if (settings.health) {
            DrawHealthBar(box, player.health);
        }
        
        if (settings.armor && player.armor > 0) {
            DrawArmorBar(box, player.armor);
        }
        
        // Texto acima da caixa
        Vector2 textPos = Vector2(box.min.x + box.width / 2.0f, box.min.y - 5.0f);
        
        if (settings.names && !player.name.empty()) {
            DrawPlayerName(textPos, player.name, color);
            textPos.y -= settings.fontSize + 2;
        }
        
        if (settings.distance) {
            std::string distText = std::to_string((int)player.distance) + "m";
            DrawDistance(textPos, player.distance, color);
            textPos.y -= settings.fontSize + 2;
        }
        
        if (settings.snaplines) {
            DrawSnapline(screenCenter, Vector2(box.min.x + box.width / 2.0f, box.max.y), color);
        }
    }
}

bool ESP::ShouldDrawPlayer(const EntityInfo& player, int localTeam) {
    // Verificações básicas
    if (!player.onScreen) return false;
    if (player.distance > settings.maxDistance) return false;
    
    // Filtro de mortos
    if (!settings.showDead && !player.alive) return false;
    
    // Filtro de espectadores
    if (!settings.showSpectators && player.team == Constants::TEAM_SPECTATOR) return false;
    
    // Team check
    if (settings.teamCheck && player.team == localTeam && localTeam != Constants::TEAM_NONE) {
        return false;
    }
    
    // Visible only
    if (settings.visibleOnly && !IsPlayerVisible(player)) {
        return false;
    }
    
    return true;
}

void ESP::GetPlayerColor(const EntityInfo& player, int localTeam, float color[4]) {
    // Se é do mesmo time
    if (player.team == localTeam && localTeam != Constants::TEAM_NONE) {
        std::memcpy(color, settings.teamColor, sizeof(float) * 4);
    }
    // Se é inimigo
    else {
        std::memcpy(color, settings.enemyColor, sizeof(float) * 4);
    }

    // Se está visível, usar cor especial
    if (settings.visibleOnly && IsPlayerVisible(player)) {
        std::memcpy(color, settings.visibleColor, sizeof(float) * 4);
    }
}

BoundingBox ESP::CalculateBoundingBox(const EntityInfo& player) {
    // ESP EXTERNO COMPLETAMENTE DESABILITADO
    return BoundingBox();
}

bool ESP::IsPlayerVisible(const EntityInfo& player) {
    // Por enquanto, usar o flag spotted do jogo
    // Em uma implementação mais avançada, poderia fazer ray tracing
    return player.spotted;
}

void ESP::DrawBox(const BoundingBox& box, const float color[4]) {
    // ESP EXTERNO COMPLETAMENTE DESABILITADO
    return;
}

void ESP::DrawCornerBox(const BoundingBox& box, const float color[4]) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImU32 imColor = ImGui::ColorConvertFloat4ToU32(ImVec4(color[0], color[1], color[2], color[3]));
    
    float cornerLength = Min(box.width, box.height) * 0.25f;
    
    // Cantos superiores
    // Esquerdo
    drawList->AddLine(ImVec2(box.min.x, box.min.y), ImVec2(box.min.x + cornerLength, box.min.y), imColor, settings.boxThickness);
    drawList->AddLine(ImVec2(box.min.x, box.min.y), ImVec2(box.min.x, box.min.y + cornerLength), imColor, settings.boxThickness);
    
    // Direito
    drawList->AddLine(ImVec2(box.max.x, box.min.y), ImVec2(box.max.x - cornerLength, box.min.y), imColor, settings.boxThickness);
    drawList->AddLine(ImVec2(box.max.x, box.min.y), ImVec2(box.max.x, box.min.y + cornerLength), imColor, settings.boxThickness);
    
    // Cantos inferiores
    // Esquerdo
    drawList->AddLine(ImVec2(box.min.x, box.max.y), ImVec2(box.min.x + cornerLength, box.max.y), imColor, settings.boxThickness);
    drawList->AddLine(ImVec2(box.min.x, box.max.y), ImVec2(box.min.x, box.max.y - cornerLength), imColor, settings.boxThickness);
    
    // Direito
    drawList->AddLine(ImVec2(box.max.x, box.max.y), ImVec2(box.max.x - cornerLength, box.max.y), imColor, settings.boxThickness);
    drawList->AddLine(ImVec2(box.max.x, box.max.y), ImVec2(box.max.x, box.max.y - cornerLength), imColor, settings.boxThickness);
}

void ESP::DrawHealthBar(const BoundingBox& box, int health, int maxHealth) {
    if (health <= 0) return;

    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    float barWidth = 4.0f;
    float barHeight = box.height;
    float barX = box.min.x - barWidth - 2.0f;
    float barY = box.min.y;

    // Background da barra
    ImU32 bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 0.8f));
    drawList->AddRectFilled(
        ImVec2(barX, barY),
        ImVec2(barX + barWidth, barY + barHeight),
        bgColor
    );

    // Calcular cor baseada na vida
    float healthPercent = (float)health / (float)maxHealth;
    ImVec4 healthColor;

    if (healthPercent > 0.6f) {
        healthColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f); // Verde
    } else if (healthPercent > 0.3f) {
        healthColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // Amarelo
    } else {
        healthColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho
    }

    // Barra de vida
    float healthBarHeight = barHeight * healthPercent;
    ImU32 healthImColor = ImGui::ColorConvertFloat4ToU32(healthColor);

    drawList->AddRectFilled(
        ImVec2(barX, barY + barHeight - healthBarHeight),
        ImVec2(barX + barWidth, barY + barHeight),
        healthImColor
    );

    // Texto da vida
    if (health < maxHealth) {
        std::string healthText = std::to_string(health);
        ImVec2 textSize = ImGui::CalcTextSize(healthText.c_str());
        drawList->AddText(
            ImVec2(barX - textSize.x - 2.0f, barY + barHeight - healthBarHeight - textSize.y / 2.0f),
            ImGui::ColorConvertFloat4ToU32(ImVec4(1.0f, 1.0f, 1.0f, 1.0f)),
            healthText.c_str()
        );
    }
}

void ESP::DrawArmorBar(const BoundingBox& box, int armor, int maxArmor) {
    if (armor <= 0) return;

    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    float barWidth = 4.0f;
    float barHeight = box.height;
    float barX = box.max.x + 2.0f;
    float barY = box.min.y;

    // Background da barra
    ImU32 bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 0.8f));
    drawList->AddRectFilled(
        ImVec2(barX, barY),
        ImVec2(barX + barWidth, barY + barHeight),
        bgColor
    );

    // Barra de armor (azul)
    float armorPercent = (float)armor / (float)maxArmor;
    float armorBarHeight = barHeight * armorPercent;
    ImU32 armorColor = ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.5f, 1.0f, 1.0f));

    drawList->AddRectFilled(
        ImVec2(barX, barY + barHeight - armorBarHeight),
        ImVec2(barX + barWidth, barY + barHeight),
        armorColor
    );
}

void ESP::DrawPlayerName(const Vector2& position, const std::string& name, const float color[4]) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImVec2 textSize = ImGui::CalcTextSize(name.c_str());
    ImVec2 textPos = ImVec2(position.x - textSize.x / 2.0f, position.y - textSize.y);

    // Sombra do texto
    drawList->AddText(
        ImVec2(textPos.x + 1, textPos.y + 1),
        ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)),
        name.c_str()
    );

    // Texto principal
    drawList->AddText(
        textPos,
        ImGui::ColorConvertFloat4ToU32(ImVec4(color[0], color[1], color[2], color[3])),
        name.c_str()
    );
}

void ESP::DrawDistance(const Vector2& position, float distance, const float color[4]) {
    std::string distText = std::to_string((int)distance) + "m";

    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImVec2 textSize = ImGui::CalcTextSize(distText.c_str());
    ImVec2 textPos = ImVec2(position.x - textSize.x / 2.0f, position.y - textSize.y);

    // Sombra do texto
    drawList->AddText(
        ImVec2(textPos.x + 1, textPos.y + 1),
        ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)),
        distText.c_str()
    );

    // Texto principal
    drawList->AddText(
        textPos,
        ImGui::ColorConvertFloat4ToU32(ImVec4(color[0], color[1], color[2], color[3])),
        distText.c_str()
    );
}

void ESP::DrawSnapline(const Vector2& start, const Vector2& end, const float color[4]) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImU32 imColor = ImGui::ColorConvertFloat4ToU32(ImVec4(color[0], color[1], color[2], color[3]));

    drawList->AddLine(
        ImVec2(start.x, start.y),
        ImVec2(end.x, end.y),
        imColor,
        settings.snaplineThickness
    );
}

void ESP::DrawWeaponName(const Vector2& position, const std::string& weapon, const float color[4]) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImVec2 textSize = ImGui::CalcTextSize(weapon.c_str());
    ImVec2 textPos = ImVec2(position.x - textSize.x / 2.0f, position.y);

    // Sombra do texto
    drawList->AddText(
        ImVec2(textPos.x + 1, textPos.y + 1),
        ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)),
        weapon.c_str()
    );

    // Texto principal
    drawList->AddText(
        textPos,
        ImGui::ColorConvertFloat4ToU32(ImVec4(color[0], color[1], color[2], color[3])),
        weapon.c_str()
    );
}
