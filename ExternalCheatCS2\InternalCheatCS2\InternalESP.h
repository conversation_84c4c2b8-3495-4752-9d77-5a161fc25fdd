#pragma once
#include "pch.h"
#include "InternalMemory.h"
#include "DirectXHook.h"

class InternalESP {
private:
    InternalMemory* m_pMemory;
    DirectXHook* m_pDirectXHook;

    // DirectX rendering objects para renderização INTERNA
    ID3D11Buffer* m_pVertexBuffer;
    ID3D11VertexShader* m_pVertexShader;
    ID3D11PixelShader* m_pPixelShader;
    ID3D11InputLayout* m_pInputLayout;
    ID3D11Buffer* m_pConstantBuffer;
    ID3D11BlendState* m_pBlendState;
    ID3D11RasterizerState* m_pRasterizerState;

    // Vertex structure para DirectX
    struct Vertex {
        float x, y, z;
        float r, g, b, a;
    };

    // Constant buffer para shaders
    struct ConstantBuffer {
        float screenWidth;
        float screenHeight;
        float padding[2];
    };

    bool m_bInitialized;
    std::vector<PlayerInfo> m_cachedPlayers;
    
public:
    InternalESP(InternalMemory* memory, DirectXHook* dxHook);
    ~InternalESP();
    
    bool Initialize();
    void Update();
    void Render();
    void Shutdown();

    // Método para RealESP acessar dados dos players
    const std::vector<PlayerInfo>& GetCachedPlayers() const { return m_cachedPlayers; }
    bool IsEnabled() const { return true; } // Sempre habilitado para coleta de dados
    
private:
    bool CreateDirectXResources();
    void CleanupDirectXResources();
    bool CreateShaders();
    bool CreateBuffers();

    // Rendering functions DirectX INTERNO
    void RenderBox(float x, float y, float width, float height, const Color& color);
    void RenderLine(float x1, float y1, float x2, float y2, const Color& color);
    void RenderText(float x, float y, const std::string& text, const Color& color);

    // Helper functions
    void DrawPlayerESP(const PlayerInfo& player);
    void RenderPlayerDirectly(const PlayerInfo& player);
    Color GetPlayerColor(const PlayerInfo& player);
};
