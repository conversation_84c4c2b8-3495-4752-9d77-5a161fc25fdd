#include "Overlay.h"
#include "../imgui/imgui.h"
#include "../imgui/backends/imgui_impl_win32.h"
#include "../imgui/backends/imgui_impl_dx11.h"
#include <iostream>

// Forward declaration for ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

Overlay::Overlay() 
    : overlayWindow(nullptr), targetWindow(nullptr), device(nullptr), deviceContext(nullptr),
      swapChain(nullptr), renderTargetView(nullptr), initialized(false), running(false),
      menuVisible(false), windowWidth(1920), windowHeight(1080) {
}

Overlay::~Overlay() {
    Shutdown();
}

bool Overlay::Initialize() {
    std::cout << "[Overlay] Inicializando overlay..." << std::endl;
    
    // Encontrar janela do CS2
    targetWindow = FindTargetWindow();
    if (!targetWindow) {
        std::cout << "[ERRO] Janela do CS2 não encontrada!" << std::endl;
        return false;
    }
    
    std::cout << "[Overlay] Janela do CS2 encontrada!" << std::endl;
    
    // Obter tamanho da janela alvo
    RECT rect;
    GetClientRect(targetWindow, &rect);
    windowWidth = rect.right - rect.left;
    windowHeight = rect.bottom - rect.top;
    
    // Criar janela overlay
    if (!CreateOverlayWindow()) {
        std::cout << "[ERRO] Falha ao criar janela overlay!" << std::endl;
        return false;
    }
    
    // Inicializar DirectX
    if (!InitializeDirectX()) {
        std::cout << "[ERRO] Falha ao inicializar DirectX!" << std::endl;
        return false;
    }
    
    // Inicializar ImGui
    if (!InitializeImGui()) {
        std::cout << "[ERRO] Falha ao inicializar ImGui!" << std::endl;
        return false;
    }
    
    initialized = true;
    running = true;
    
    std::cout << "[Overlay] Overlay inicializado com sucesso!" << std::endl;
    return true;
}

void Overlay::Shutdown() {
    if (!initialized) return;
    
    running = false;
    
    CleanupImGui();
    CleanupDirectX();
    CleanupWindow();
    
    initialized = false;
}

HWND Overlay::FindTargetWindow() {
    // Procurar por diferentes nomes de janela do CS2
    HWND hwnd = FindWindowW(nullptr, L"Counter-Strike 2");
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"cs2");
    }
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"CS2");
    }
    
    return hwnd;
}

bool Overlay::CreateOverlayWindow() {
    // Registrar classe da janela
    windowClass = {
        sizeof(WNDCLASSEXW),
        0,
        WindowProc,
        0,
        0,
        GetModuleHandleW(nullptr),
        nullptr,
        nullptr,
        nullptr,
        nullptr,
        L"CS2OverlayClass",
        nullptr
    };
    
    if (!RegisterClassExW(&windowClass)) {
        return false;
    }
    
    // Obter posição da janela alvo
    RECT targetRect;
    GetWindowRect(targetWindow, &targetRect);
    
    // Criar janela overlay
    overlayWindow = CreateWindowExW(
        WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED,
        windowClass.lpszClassName,
        L"CS2 Overlay",
        WS_POPUP,
        targetRect.left,
        targetRect.top,
        windowWidth,
        windowHeight,
        nullptr,
        nullptr,
        windowClass.hInstance,
        this
    );
    
    if (!overlayWindow) {
        return false;
    }
    
    // Configurar transparência
    SetLayeredWindowAttributes(overlayWindow, RGB(0, 0, 0), 255, LWA_ALPHA);
    
    ShowWindow(overlayWindow, SW_SHOW);
    UpdateWindow(overlayWindow);
    
    return true;
}

bool Overlay::InitializeDirectX() {
    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 2;
    swapChainDesc.BufferDesc.Width = windowWidth;
    swapChainDesc.BufferDesc.Height = windowHeight;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferDesc.RefreshRate.Numerator = 60;
    swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
    swapChainDesc.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = overlayWindow;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.SampleDesc.Quality = 0;
    swapChainDesc.Windowed = TRUE;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;
    
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0 };
    
    HRESULT result = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        featureLevelArray,
        2,
        D3D11_SDK_VERSION,
        &swapChainDesc,
        &swapChain,
        &device,
        &featureLevel,
        &deviceContext
    );
    
    if (FAILED(result)) {
        return false;
    }
    
    // Criar render target view
    ID3D11Texture2D* backBuffer;
    swapChain->GetBuffer(0, IID_PPV_ARGS(&backBuffer));
    device->CreateRenderTargetView(backBuffer, nullptr, &renderTargetView);
    backBuffer->Release();
    
    return true;
}

bool Overlay::InitializeImGui() {
    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    
    // Setup ImGui style
    ImGui::StyleColorsDark();
    
    // Setup Platform/Renderer backends
    if (!ImGui_ImplWin32_Init(overlayWindow)) {
        return false;
    }
    
    if (!ImGui_ImplDX11_Init(device, deviceContext)) {
        return false;
    }
    
    return true;
}

void Overlay::CleanupImGui() {
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
}

void Overlay::CleanupDirectX() {
    if (renderTargetView) {
        renderTargetView->Release();
        renderTargetView = nullptr;
    }
    
    if (swapChain) {
        swapChain->Release();
        swapChain = nullptr;
    }
    
    if (deviceContext) {
        deviceContext->Release();
        deviceContext = nullptr;
    }
    
    if (device) {
        device->Release();
        device = nullptr;
    }
}

void Overlay::CleanupWindow() {
    if (overlayWindow) {
        DestroyWindow(overlayWindow);
        overlayWindow = nullptr;
    }
    
    UnregisterClassW(windowClass.lpszClassName, windowClass.hInstance);
}

LRESULT CALLBACK Overlay::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (uMsg == WM_CREATE) {
        CREATESTRUCT* createStruct = reinterpret_cast<CREATESTRUCT*>(lParam);
        SetWindowLongPtrW(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(createStruct->lpCreateParams));
        return 0;
    }
    
    Overlay* overlay = reinterpret_cast<Overlay*>(GetWindowLongPtrW(hwnd, GWLP_USERDATA));
    if (overlay) {
        return overlay->HandleMessage(hwnd, uMsg, wParam, lParam);
    }
    
    return DefWindowProcW(hwnd, uMsg, wParam, lParam);
}

LRESULT Overlay::HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hwnd, uMsg, wParam, lParam)) {
        return true;
    }
    
    switch (uMsg) {
        case WM_DESTROY:
            running = false;
            PostQuitMessage(0);
            return 0;
            
        case WM_KEYDOWN:
            if (wParam == VK_INSERT) {
                ToggleMenu();
            }
            break;
    }
    
    return DefWindowProcW(hwnd, uMsg, wParam, lParam);
}

void Overlay::BeginFrame() {
    if (!initialized || !running) return;

    // Processar mensagens da janela
    MSG msg;
    while (PeekMessageW(&msg, nullptr, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessageW(&msg);

        if (msg.message == WM_QUIT) {
            running = false;
            return;
        }
    }

    // Atualizar posição da janela overlay
    UpdateWindowPosition();

    // Começar frame do ImGui
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();
}

void Overlay::EndFrame() {
    if (!initialized || !running) return;

    // Renderizar ImGui
    ImGui::Render();

    // Limpar render target
    const float clearColor[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
    deviceContext->OMSetRenderTargets(1, &renderTargetView, nullptr);
    deviceContext->ClearRenderTargetView(renderTargetView, clearColor);

    // Renderizar dados do ImGui
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

    // Apresentar frame
    swapChain->Present(1, 0);
}

void Overlay::RenderMenu() {
    if (!menuVisible) return;

    // Configurar estilo da janela
    ImGui::SetNextWindowSize(ImVec2(400, 500), ImGuiCond_FirstUseEver);
    ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);

    // Começar janela principal
    if (ImGui::Begin("CS2 External Cheat", &menuVisible, ImGuiWindowFlags_NoCollapse)) {

        // Header
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "CS2 External ESP");
        ImGui::Separator();

        // ESP Settings
        if (ImGui::CollapsingHeader("ESP Settings", ImGuiTreeNodeFlags_DefaultOpen)) {

            // Configurações básicas
            ImGui::Text("Basic Settings:");

            // Placeholder para configurações do ESP
            // Estas serão conectadas ao ESP na próxima etapa
            static bool espEnabled = true;
            static bool boxes = true;
            static bool names = true;
            static bool health = true;
            static bool armor = false;
            static bool distance = true;
            static bool snaplines = false;
            static bool teamCheck = true;

            ImGui::Checkbox("Enable ESP", &espEnabled);
            ImGui::Checkbox("Boxes", &boxes);
            ImGui::Checkbox("Names", &names);
            ImGui::Checkbox("Health", &health);
            ImGui::Checkbox("Armor", &armor);
            ImGui::Checkbox("Distance", &distance);
            ImGui::Checkbox("Snaplines", &snaplines);
            ImGui::Checkbox("Team Check", &teamCheck);

            ImGui::Separator();

            // Configurações de cores
            ImGui::Text("Colors:");
            static float enemyColor[4] = { 1.0f, 0.0f, 0.0f, 1.0f };
            static float teamColor[4] = { 0.0f, 1.0f, 0.0f, 1.0f };

            ImGui::ColorEdit4("Enemy Color", enemyColor);
            ImGui::ColorEdit4("Team Color", teamColor);

            ImGui::Separator();

            // Configurações avançadas
            ImGui::Text("Advanced:");
            static float maxDistance = 1000.0f;
            static float boxThickness = 1.0f;

            ImGui::SliderFloat("Max Distance", &maxDistance, 100.0f, 2000.0f, "%.0f");
            ImGui::SliderFloat("Box Thickness", &boxThickness, 0.5f, 5.0f, "%.1f");
        }

        ImGui::Separator();

        // Informações
        if (ImGui::CollapsingHeader("Information")) {
            ImGui::Text("Status: Running");
            ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
            ImGui::Text("Press INSERT to toggle menu");
        }

        // Botão para fechar
        if (ImGui::Button("Close Menu")) {
            menuVisible = false;
        }
    }
    ImGui::End();
}

void Overlay::UpdateWindowPosition() {
    if (!targetWindow || !overlayWindow) return;

    RECT targetRect;
    if (GetWindowRect(targetWindow, &targetRect)) {
        int newWidth = targetRect.right - targetRect.left;
        int newHeight = targetRect.bottom - targetRect.top;

        // Atualizar posição e tamanho se necessário
        if (newWidth != windowWidth || newHeight != windowHeight) {
            windowWidth = newWidth;
            windowHeight = newHeight;

            SetWindowPos(
                overlayWindow,
                HWND_TOPMOST,
                targetRect.left,
                targetRect.top,
                windowWidth,
                windowHeight,
                SWP_NOACTIVATE
            );
        }
    }
}

void Overlay::SetWindowSize(int width, int height) {
    windowWidth = width;
    windowHeight = height;

    if (overlayWindow) {
        SetWindowPos(overlayWindow, nullptr, 0, 0, width, height, SWP_NOMOVE | SWP_NOZORDER);
    }
}
