#pragma once

#include "Memory.h"
#include <vector>
#include <string>

struct ESPSettings {
    bool enabled = true;
    bool boxes = true;
    bool names = true;
    bool health = true;
    bool armor = false;
    bool distance = true;
    bool weapons = false;
    bool snaplines = false;
    bool teamCheck = true;
    bool visibleOnly = false;
    
    // Cores
    float enemyColor[4] = { 1.0f, 0.0f, 0.0f, 1.0f }; // Vermelho
    float teamColor[4] = { 0.0f, 1.0f, 0.0f, 1.0f };  // Verde
    float visibleColor[4] = { 1.0f, 1.0f, 0.0f, 1.0f }; // Amarelo
    
    // Configurações de renderização
    float boxThickness = 1.0f;
    float snaplineThickness = 1.0f;
    int fontSize = 13;
    float maxDistance = 1000.0f;
    
    // Filtros
    bool showSpectators = false;
    bool showDead = false;
};

struct BoundingBox {
    Vector2 min;
    Vector2 max;
    float width;
    float height;
    bool valid;
    
    BoundingBox() : valid(false) {}
    
    BoundingBox(const Vector2& topLeft, const Vector2& bottomRight) {
        min = topLeft;
        max = bottomRight;
        width = max.x - min.x;
        height = max.y - min.y;
        valid = true;
    }
};

class ESP {
private:
    Memory* memory;
    ESPSettings settings;
    std::vector<EntityInfo> players;
    Vector2 screenCenter;
    
    // Funções auxiliares
    BoundingBox CalculateBoundingBox(const EntityInfo& player);
    bool IsPlayerVisible(const EntityInfo& player);
    bool ShouldDrawPlayer(const EntityInfo& player, int localTeam);
    void GetPlayerColor(const EntityInfo& player, int localTeam, float color[4]);
    
    // Funções de renderização
    void DrawBox(const BoundingBox& box, const float color[4]);
    void DrawFilledBox(const BoundingBox& box, const float color[4], float alpha = 0.2f);
    void DrawCornerBox(const BoundingBox& box, const float color[4]);
    void DrawHealthBar(const BoundingBox& box, int health, int maxHealth = 100);
    void DrawArmorBar(const BoundingBox& box, int armor, int maxArmor = 100);
    void DrawPlayerName(const Vector2& position, const std::string& name, const float color[4]);
    void DrawDistance(const Vector2& position, float distance, const float color[4]);
    void DrawSnapline(const Vector2& start, const Vector2& end, const float color[4]);
    void DrawWeaponName(const Vector2& position, const std::string& weapon, const float color[4]);
    
public:
    ESP(Memory* mem);
    ~ESP();
    
    void Update();
    void Render();
    
    // Getters/Setters
    ESPSettings& GetSettings() { return settings; }
    const std::vector<EntityInfo>& GetPlayers() const { return players; }
    
    // Configurações
    void SetEnabled(bool enabled) { settings.enabled = enabled; }
    bool IsEnabled() const { return settings.enabled; }
    
    void SetBoxes(bool enabled) { settings.boxes = enabled; }
    void SetNames(bool enabled) { settings.names = enabled; }
    void SetHealth(bool enabled) { settings.health = enabled; }
    void SetArmor(bool enabled) { settings.armor = enabled; }
    void SetDistance(bool enabled) { settings.distance = enabled; }
    void SetWeapons(bool enabled) { settings.weapons = enabled; }
    void SetSnaplines(bool enabled) { settings.snaplines = enabled; }
    void SetTeamCheck(bool enabled) { settings.teamCheck = enabled; }
    void SetVisibleOnly(bool enabled) { settings.visibleOnly = enabled; }
    
    void SetMaxDistance(float distance) { settings.maxDistance = distance; }
    void SetBoxThickness(float thickness) { settings.boxThickness = thickness; }
    void SetFontSize(int size) { settings.fontSize = size; }
    
    // Cores
    void SetEnemyColor(float r, float g, float b, float a = 1.0f) {
        settings.enemyColor[0] = r;
        settings.enemyColor[1] = g;
        settings.enemyColor[2] = b;
        settings.enemyColor[3] = a;
    }
    
    void SetTeamColor(float r, float g, float b, float a = 1.0f) {
        settings.teamColor[0] = r;
        settings.teamColor[1] = g;
        settings.teamColor[2] = b;
        settings.teamColor[3] = a;
    }
};
