#include "pch.h"
#include "RealESP.h"
#include <iostream>
#include <algorithm>
#include <chrono>

// Estrutura para bounding box PROFISSIONAL como nos exemplos
struct BoundingBox {
    float x, y, width, height;
    bool valid;

    BoundingBox() : x(0), y(0), width(0), height(0), valid(false) {}

    BoundingBox(const PlayerInfo& player, InternalMemory* memory) {
        // MÉTODO COMPLETAMENTE FIXO - SEM CÁLCULOS DINÂMICOS
        Vector3 worldPos = player.position;
        Vector2 feetScreen;

        // Converter apenas posição dos pés para tela
        if (!memory->WorldToScreen(worldPos, feetScreen)) {
            valid = false;
            return;
        }

        // TAMANHO COMPLETAMENTE FIXO - NUNCA VARIA
        height = 60.0f;  // SEMPRE 60 pixels de altura
        width = 40.0f;   // SEMPRE 40 pixels de largura

        // POSIÇÃO FIXA - Box centralizado nos pés, altura fixa para cima
        x = feetScreen.x - width / 2.0f;   // Centro horizontal
        y = feetScreen.y - height;         // Altura fixa acima dos pés

        // Sempre válido se chegou até aqui
        valid = true;
    }
};

RealESP::RealESP(InternalMemory* memory) : m_pMemory(memory), m_enabled(true) {
}

RealESP::~RealESP() {
}

void RealESP::DrawBox(HDC hdc, const BoundingBox& bbox, COLORREF color, int thickness) {
    if (!bbox.valid) return;
    
    HPEN pen = CreatePen(PS_SOLID, thickness, color);
    HPEN oldPen = (HPEN)SelectObject(hdc, pen);
    
    // Desenhar box vazio (apenas bordas)
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, GetStockObject(NULL_BRUSH));
    
    Rectangle(hdc, 
             (int)bbox.x, 
             (int)bbox.y, 
             (int)(bbox.x + bbox.width), 
             (int)(bbox.y + bbox.height));
    
    SelectObject(hdc, oldPen);
    SelectObject(hdc, oldBrush);
    DeleteObject(pen);
}

void RealESP::DrawHealthBar(HDC hdc, const BoundingBox& bbox, int health, int maxHealth) {
    if (!bbox.valid) return;
    
    float healthPercent = (float)health / (float)maxHealth;
    
    // Cores baseadas na saúde
    COLORREF healthColor = RGB(0, 255, 0);  // Verde
    if (healthPercent < 0.5f) healthColor = RGB(255, 255, 0); // Amarelo
    if (healthPercent < 0.25f) healthColor = RGB(255, 0, 0);  // Vermelho
    
    // Posição da barra de vida (à esquerda do box)
    float barWidth = 4.0f;
    float barHeight = bbox.height * healthPercent;
    float barX = bbox.x - barWidth - 2;
    float barY = bbox.y + bbox.height - barHeight;
    
    // Fundo da barra (cinza)
    HBRUSH bgBrush = CreateSolidBrush(RGB(60, 60, 60));
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, bgBrush);
    
    Rectangle(hdc, 
             (int)barX, 
             (int)bbox.y, 
             (int)(barX + barWidth), 
             (int)(bbox.y + bbox.height));
    
    // Barra de vida
    HBRUSH healthBrush = CreateSolidBrush(healthColor);
    SelectObject(hdc, healthBrush);
    
    Rectangle(hdc, 
             (int)barX, 
             (int)barY, 
             (int)(barX + barWidth), 
             (int)(bbox.y + bbox.height));
    
    SelectObject(hdc, oldBrush);
    DeleteObject(bgBrush);
    DeleteObject(healthBrush);
}

void RealESP::DrawPlayerInfo(HDC hdc, const BoundingBox& bbox, const PlayerInfo& player) {
    if (!bbox.valid) return;
    
    SetTextColor(hdc, RGB(255, 255, 255));
    SetBkMode(hdc, TRANSPARENT);
    
    // Nome do player acima do box
    char nameText[64];
    sprintf_s(nameText, "Player");
    TextOutA(hdc,
            (int)(bbox.x + bbox.width/2 - 25),
            (int)(bbox.y - 20),
            nameText, (int)strlen(nameText));
    
    // HP à direita do box
    char hpText[32];
    sprintf_s(hpText, "%d HP", player.health);
    TextOutA(hdc,
            (int)(bbox.x + bbox.width + 5),
            (int)(bbox.y),
            hpText, (int)strlen(hpText));
    
    // Distância abaixo do box
    char distText[32];
    sprintf_s(distText, "%.0fm", player.distance);
    TextOutA(hdc,
            (int)(bbox.x + bbox.width/2 - 15),
            (int)(bbox.y + bbox.height + 5),
            distText, (int)strlen(distText));
}

void RealESP::RenderPlayer(const PlayerInfo& player) {
    if (!player.valid || !m_enabled) return;

    // COORDENADAS COMPLETAMENTE FIXAS - SEM CÁLCULOS DINÂMICOS
    static int playerIndex = 0;
    playerIndex++;

    // Posições fixas na tela (grid 3x3)
    int gridX = (playerIndex % 3) * 300 + 200; // 200, 500, 800
    int gridY = ((playerIndex / 3) % 3) * 200 + 200; // 200, 400, 600

    // TAMANHO COMPLETAMENTE FIXO
    int fixedWidth = 40;
    int fixedHeight = 60;

    // RENDERIZAÇÃO DIRETA E SIMPLES
    HDC screenDC = GetDC(NULL);
    if (!screenDC) return;

    try {
        // Cor FIXA baseada no team
        COLORREF boxColor = (player.team == 2) ? RGB(255, 255, 0) : RGB(0, 255, 255); // Amarelo T, Ciano CT

        // Desenhar box GROSSO e VISÍVEL
        HPEN pen = CreatePen(PS_SOLID, 4, boxColor);
        HPEN oldPen = (HPEN)SelectObject(screenDC, pen);
        HBRUSH oldBrush = (HBRUSH)SelectObject(screenDC, GetStockObject(NULL_BRUSH));

        // Desenhar retângulo em posição FIXA
        Rectangle(screenDC,
                 gridX,
                 gridY,
                 gridX + fixedWidth,
                 gridY + fixedHeight);

        SelectObject(screenDC, oldPen);
        SelectObject(screenDC, oldBrush);
        DeleteObject(pen);

        // Log ocasional
        static int renderCount = 0;
        if (renderCount % 200 == 0) {
            std::cout << "[REAL ESP] Player HP=" << player.health
                      << " Team=" << player.team
                      << " Dist=" << (int)player.distance << "m"
                      << " Box=(" << gridX << "," << gridY
                      << "," << fixedWidth << "," << fixedHeight << ") POSICAO_FIXA" << std::endl;
        }
        renderCount++;

    } catch (...) {
        // Ignorar erros
    }

    ReleaseDC(NULL, screenDC);
}

void RealESP::Render(const std::vector<PlayerInfo>& players) {
    if (!m_enabled) return;

    // RENDERIZAÇÃO SEM LIMITAÇÃO - ELIMINAR PISCAMENTO COMPLETAMENTE
    // Sem controle de FPS - renderizar sempre que chamado

    // Renderizar TODOS os players válidos
    int playersRendered = 0;
    for (const auto& player : players) {
        // Filtrar apenas players básicos válidos
        if (player.valid && player.health > 0) {
            RenderPlayer(player);
            playersRendered++;
        }
    }

    // Log de status ocasional
    static int logCount = 0;
    if (logCount % 200 == 0) {
        std::cout << "[REAL ESP] Renderizados " << playersRendered << " players (SEM_LIMITACAO_FPS)" << std::endl;
    }
    logCount++;
}

void RealESP::SetEnabled(bool enabled) {
    m_enabled = enabled;
}

bool RealESP::IsEnabled() const {
    return m_enabled;
}
