#include "pch.h"
#include "CheatCore.h"
#include <thread>
#include <iostream>
#include <io.h>
#include <fcntl.h>

// Instância global do cheat
CheatCore* g_pCheat = nullptr;

// Handler para exceções não tratadas
LONG WINAPI UnhandledExceptionHandler(EXCEPTION_POINTERS* pExceptionInfo) {
    std::cout << "[CRASH DETECTADO] Código: 0x" << std::hex << pExceptionInfo->ExceptionRecord->ExceptionCode << std::dec << std::endl;
    std::cout << "[CRASH] Endereço: 0x" << std::hex << pExceptionInfo->ExceptionRecord->ExceptionAddress << std::dec << std::endl;
    std::cout << "[CRASH] Ejetando cheat para evitar crash do jogo..." << std::endl;

    // Cleanup de emergência
    if (g_pCheat) {
        try {
            g_pCheat->SetRunning(false);
        } catch (...) {}
    }

    return EXCEPTION_EXECUTE_HANDLER;
}

// Thread principal do cheat
DWORD WINAPI CheatThread(LPVOID lpParam) {
    // Instalar handler de exceção
    SetUnhandledExceptionFilter(UnhandledExceptionHandler);

    try {
        // Alocar console para debug
        AllocConsole();
        FILE* pCout;
        freopen_s(&pCout, "CONOUT$", "w", stdout);

        std::cout << "[INTERNAL CHEAT] Iniciando com proteção anti-crash..." << std::endl;

        // Aguardar o jogo carregar completamente
        std::this_thread::sleep_for(std::chrono::seconds(3));

        // Inicializar cheat
        g_pCheat = new CheatCore();
        if (!g_pCheat->Initialize()) {
            std::cout << "[ERRO] Falha ao inicializar cheat!" << std::endl;
            return 1;
        }

        std::cout << "[INTERNAL CHEAT] Cheat inicializado com sucesso!" << std::endl;
        std::cout << "[CONTROLES] INSERT = Toggle Menu | DELETE = Eject" << std::endl;

        // Loop principal com proteção
        while (g_pCheat->IsRunning()) {
            try {
                g_pCheat->Update();
                std::this_thread::sleep_for(std::chrono::milliseconds(10)); // Mais lento para estabilidade
            } catch (...) {
                std::cout << "[ERRO] Exceção no loop principal, continuando..." << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        // Cleanup
        std::cout << "[INTERNAL CHEAT] Finalizando..." << std::endl;
        if (g_pCheat) {
            g_pCheat->Shutdown();
            delete g_pCheat;
            g_pCheat = nullptr;
        }

        // Fechar console
        if (pCout) fclose(pCout);
        FreeConsole();

    } catch (const std::exception& e) {
        std::cout << "[ERRO CRÍTICO] " << e.what() << std::endl;
    } catch (...) {
        std::cout << "[ERRO CRÍTICO] Exceção desconhecida!" << std::endl;
    }

    // Ejetar DLL
    FreeLibraryAndExitThread(static_cast<HMODULE>(lpParam), 0);
    return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Desabilitar notificações de thread
        DisableThreadLibraryCalls(hModule);
        
        // Criar thread principal do cheat
        CreateThread(nullptr, 0, CheatThread, hModule, 0, nullptr);
        break;
        
    case DLL_PROCESS_DETACH:
        // Cleanup se necessário
        if (g_pCheat) {
            g_pCheat->Shutdown();
            delete g_pCheat;
            g_pCheat = nullptr;
        }
        break;
    }
    return TRUE;
}
