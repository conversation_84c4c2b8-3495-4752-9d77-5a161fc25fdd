#pragma once
#include "pch.h"

// Offsets do CS2 (EXATOS do Cheat Engine que FUNCIONA)
namespace Offsets {
    namespace Client {
        constexpr uintptr_t dwEntityList = 0x18652B0;  // EXATO do Cheat Engine que funciona
        constexpr uintptr_t dwLocalPlayerController = 0x1A52D20;
        constexpr uintptr_t dwLocalPlayerPawn = 0x18580D0;
        constexpr uintptr_t dwViewMatrix = 0x1A6E240;
    }
    
    namespace Netvars {
        namespace BaseEntity {
            constexpr uintptr_t m_iTeamNum = 0x3E3;
            constexpr uintptr_t m_vecOrigin = 0x1290;
            constexpr uintptr_t m_lifeState = 0x348;
            constexpr uintptr_t m_iHealth = 0x344;
            constexpr uintptr_t m_hPlayerPawn = 0x80C;
            constexpr uintptr_t m_pGameSceneNode = 0x328; // Novo offset necessário
        }

        namespace PlayerPawn {
            constexpr uintptr_t m_vOldOrigin = 0x1324;
            constexpr uintptr_t m_iHealth = 0x344;
            constexpr uintptr_t m_vecViewOffset = 0xCB0;
            constexpr uintptr_t m_entitySpottedState = 0x2294;
            constexpr uintptr_t m_bSpottedByMask = 0xC;
        }

        namespace GameSceneNode {
            constexpr uintptr_t m_vecAbsOrigin = 0xD0; // Novo offset necessário
            constexpr uintptr_t m_bDormant = 0xEF;
        }
    }
}

class InternalMemory {
private:
    uintptr_t m_moduleBase;
    bool m_bInitialized;
    
    // Cache
    uintptr_t m_localPlayerController;
    uintptr_t m_localPlayerPawn;
    Matrix4x4 m_viewMatrix;
    
public:
    InternalMemory();
    ~InternalMemory();
    
    bool Initialize();
    void Shutdown();
    
    // Leitura direta de memória com verificações de segurança
    template<typename T>
    T Read(uintptr_t address) {
        if (!address || address < 0x10000) return T{};

        // Verificar se o endereço está em uma região válida
        if (address < m_moduleBase || address > m_moduleBase + 0x20000000) {
            return T{};
        }

        try {
            // Verificar se a memória é legível usando IsBadReadPtr
            if (IsBadReadPtr(reinterpret_cast<void*>(address), sizeof(T))) {
                return T{};
            }

            return *reinterpret_cast<T*>(address);
        } catch (...) {
            return T{};
        }
    }
    
    template<typename T>
    bool Write(uintptr_t address, const T& value) {
        if (!address) return false;
        try {
            *reinterpret_cast<T*>(address) = value;
            return true;
        } catch (...) {
            return false;
        }
    }
    
    // Funções específicas
    uintptr_t GetModuleBase() const { return m_moduleBase; }
    uintptr_t GetLocalPlayerController();
    uintptr_t GetLocalPlayerPawn();
    Matrix4x4 GetViewMatrix();
    
    std::vector<PlayerInfo> GetPlayers();
    
    // Utilitários
    bool WorldToScreen(const Vector3& world, Vector2& screen);
    float GetDistance(const Vector3& pos1, const Vector3& pos2);
    
private:
    bool UpdateCache();
    PlayerInfo GetPlayerInfo(uintptr_t entity);
    std::string GetPlayerName(uintptr_t controller);
};
