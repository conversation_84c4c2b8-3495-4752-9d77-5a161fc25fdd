#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>

class DLLInjector {
private:
    DWORD GetProcessId(const std::wstring& processName) {
        DWORD processId = 0;
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        
        if (snapshot != INVALID_HANDLE_VALUE) {
            PROCESSENTRY32W processEntry = {};
            processEntry.dwSize = sizeof(PROCESSENTRY32W);
            
            if (Process32FirstW(snapshot, &processEntry)) {
                do {
                    if (processName == processEntry.szExeFile) {
                        processId = processEntry.th32ProcessID;
                        break;
                    }
                } while (Process32NextW(snapshot, &processEntry));
            }
            
            CloseHandle(snapshot);
        }
        
        return processId;
    }
    
public:
    bool InjectDLL(const std::wstring& processName, const std::wstring& dllPath) {
        std::wcout << L"[INJECTOR] Procurando processo: " << processName << std::endl;
        
        // Encontrar processo
        DWORD processId = GetProcessId(processName);
        if (processId == 0) {
            std::wcout << L"[ERRO] Processo não encontrado!" << std::endl;
            return false;
        }
        
        std::wcout << L"[INJECTOR] Processo encontrado! PID: " << processId << std::endl;
        
        // Abrir processo
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::wcout << L"[ERRO] Falha ao abrir processo! Erro: " << GetLastError() << std::endl;
            return false;
        }
        
        // Alocar memória no processo alvo
        size_t dllPathSize = (dllPath.length() + 1) * sizeof(wchar_t);
        LPVOID pDllPath = VirtualAllocEx(hProcess, nullptr, dllPathSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        
        if (!pDllPath) {
            std::wcout << L"[ERRO] Falha ao alocar memória! Erro: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // Escrever caminho da DLL na memória
        if (!WriteProcessMemory(hProcess, pDllPath, dllPath.c_str(), dllPathSize, nullptr)) {
            std::wcout << L"[ERRO] Falha ao escrever na memória! Erro: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Obter endereço de LoadLibraryW
        HMODULE hKernel32 = GetModuleHandleW(L"kernel32.dll");
        LPVOID pLoadLibraryW = GetProcAddress(hKernel32, "LoadLibraryW");
        
        if (!pLoadLibraryW) {
            std::wcout << L"[ERRO] Falha ao obter LoadLibraryW!" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Criar thread remota para carregar a DLL
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0, 
            reinterpret_cast<LPTHREAD_START_ROUTINE>(pLoadLibraryW), 
            pDllPath, 0, nullptr);
        
        if (!hThread) {
            std::wcout << L"[ERRO] Falha ao criar thread remota! Erro: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        std::wcout << L"[INJECTOR] DLL injetada com sucesso!" << std::endl;
        
        // Aguardar thread terminar
        WaitForSingleObject(hThread, INFINITE);
        
        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        
        return true;
    }
};

int main() {
    std::wcout << L"=== CS2 Internal Cheat Injector ===" << std::endl;
    std::wcout << L"Desenvolvido para fins educacionais" << std::endl;
    std::wcout << L"====================================" << std::endl;
    
    DLLInjector injector;
    
    // Caminho da DLL (assumindo que está na mesma pasta)
    wchar_t currentDir[MAX_PATH];
    GetCurrentDirectoryW(MAX_PATH, currentDir);
    std::wstring dllPath = std::wstring(currentDir) + L"\\InternalCheatCS2.dll";
    
    std::wcout << L"[INFO] Caminho da DLL: " << dllPath << std::endl;
    
    // Verificar se DLL existe
    if (GetFileAttributesW(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::wcout << L"[ERRO] DLL não encontrada!" << std::endl;
        std::wcout << L"Pressione qualquer tecla para sair..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Aguardar CS2
    std::wcout << L"[INFO] Aguardando CS2 (cs2.exe)..." << std::endl;
    
    while (true) {
        if (injector.InjectDLL(L"cs2.exe", dllPath)) {
            std::wcout << L"[SUCESSO] Cheat injetado com sucesso!" << std::endl;
            std::wcout << L"[CONTROLES] INSERT = Menu | DELETE = Eject | F1 = Toggle ESP" << std::endl;
            break;
        }
        
        std::wcout << L"[INFO] CS2 não encontrado, tentando novamente em 3 segundos..." << std::endl;
        Sleep(3000);
    }
    
    std::wcout << L"Pressione qualquer tecla para sair..." << std::endl;
    std::cin.get();
    
    return 0;
}
