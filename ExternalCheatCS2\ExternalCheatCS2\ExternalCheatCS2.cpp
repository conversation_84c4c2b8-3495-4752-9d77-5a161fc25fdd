#include <iostream>
#include <Windows.h>
#include <TlHelp32.h>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <iomanip>

#include "Memory.h"
#include "Offsets.h"
#include "OverlayESP.h"
#include "GUI.h"
#include "Config.h"

void ClearScreen() {
    system("cls");
}

void PrintPlayerInfo(const std::vector<EntityInfo>& players, int localTeam) {
    std::cout << "=== CS2 External ESP - Player Information ===" << std::endl;
    std::cout << "Players encontrados: " << players.size() << std::endl;
    std::cout << "Local Team: " << (localTeam == 2 ? "Terrorist" : localTeam == 3 ? "Counter-Terrorist" : "Unknown") << std::endl;
    std::cout << "----------------------------------------" << std::endl;

    for (size_t i = 0; i < players.size(); i++) {
        const auto& player = players[i];

        std::string teamName = (player.team == 2) ? "T" : (player.team == 3) ? "CT" : "?";
        std::string enemyStatus = (player.team != localTeam) ? "[ENEMY]" : "[TEAM]";

        std::cout << "[" << std::setw(2) << i + 1 << "] " << enemyStatus << " "
                  << teamName << " | "
                  << "HP: " << std::setw(3) << player.health << " | "
                  << "Dist: " << std::setw(4) << (int)player.distance << "m | "
                  << "Pos: (" << std::setw(7) << (int)player.position.x
                  << ", " << std::setw(7) << (int)player.position.y
                  << ", " << std::setw(7) << (int)player.position.z << ")" << std::endl;
    }

    std::cout << "----------------------------------------" << std::endl;
    std::cout << "Pressione Ctrl+C para sair..." << std::endl;
}

int main()
{
    std::cout << "[CS2 External ESP] Iniciando..." << std::endl;

    // Inicializar sistema de memória
    Memory memory;
    if (!memory.Initialize()) {
        std::cout << "[ERRO] Falha ao inicializar sistema de memória!" << std::endl;
        system("pause");
        return -1;
    }

    std::cout << "[INFO] Sistema de memória inicializado com sucesso!" << std::endl;

    // Inicializar ESP
    OverlayESP esp(&memory);
    if (!esp.Initialize()) {
        std::cout << "[ERRO] Falha ao inicializar ESP!" << std::endl;
        system("pause");
        return -1;
    }

    std::cout << "[INFO] ESP inicializado com sucesso!" << std::endl;

    // Inicializar GUI
    GUI gui;
    if (!gui.Initialize()) {
        std::cout << "[AVISO] GUI não pôde ser inicializada, continuando sem interface..." << std::endl;
    } else {
        std::cout << "[INFO] GUI inicializada com sucesso!" << std::endl;
    }

    std::cout << "[INFO] ESP ativo! Pressione INSERT para abrir menu, END para pânico." << std::endl;

    // Loop principal
    while (true) {
        try {
            // Verificar modo pânico
            if (g_Config.panicMode) {
                std::cout << "[INFO] Modo pânico ativado! Encerrando..." << std::endl;
                break;
            }

            // Processar input da GUI
            gui.HandleInput();

            // Renderizar GUI se visível
            if (gui.IsVisible()) {
                gui.Render();
            }

            // ESP EXTERNO DESABILITADO - USAR APENAS CHEAT INTERNO
            // if (g_Config.esp.enabled) {
            //     esp.Update();
            //     esp.Render();
            // }

            // Mostrar informações no console apenas a cada 3 segundos (economia de CPU)
            static auto lastConsoleUpdate = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastConsoleUpdate).count() > 3000) {
                // Obter informações do local player para console
                uintptr_t localPlayer = memory.GetLocalPlayer();
                int localTeam = 0;

                if (localPlayer) {
                    localTeam = memory.Read<int>(localPlayer + Netvars::BaseEntity::m_iTeamNum);
                }

                std::vector<EntityInfo> players = memory.GetPlayers();

                ClearScreen();
                PrintPlayerInfo(players, localTeam);
                std::cout << "\n[STATUS] ESP: " << (g_Config.esp.enabled ? "ATIVO" : "INATIVO")
                         << " | GUI: " << (gui.IsVisible() ? "VISÍVEL" : "OCULTA") << std::endl;
                lastConsoleUpdate = now;
            }

            // Controle de FPS otimizado - máximo 60 FPS
            int targetFPS = min(g_Config.esp.updateRate, 60); // Limitar a 60 FPS
            int sleepTime = max(16, 1000 / targetFPS); // Mínimo 16ms (60 FPS)
            std::this_thread::sleep_for(std::chrono::milliseconds(sleepTime));

        } catch (const std::exception& e) {
            std::cout << "[ERRO] Exceção: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    gui.Shutdown();
    esp.Shutdown();
    memory.Shutdown();
    return 0;
}
