#include <iostream>
#include <Windows.h>
#include <thread>
#include <chrono>
#include "Memory.h"
#include "Offsets.h"

void TestMemorySystem() {
    std::cout << "[TEST] Testando sistema de memória..." << std::endl;
    
    Memory memory;
    if (!memory.Initialize()) {
        std::cout << "[ERRO] Falha ao inicializar sistema de memória!" << std::endl;
        return;
    }
    
    std::cout << "[INFO] Sistema de memória inicializado com sucesso!" << std::endl;
    std::cout << "[INFO] Client Base: 0x" << std::hex << memory.GetClientBase() << std::endl;
    std::cout << "[INFO] Engine2 Base: 0x" << std::hex << memory.GetEngine2Base() << std::endl;
    
    // Testar leitura do local player
    uintptr_t localPlayer = memory.GetLocalPlayer();
    std::cout << "[INFO] Local Player: 0x" << std::hex << localPlayer << std::endl;
    
    if (localPlayer) {
        // Testar leitura de dados básicos
        int health = memory.Read<int>(localPlayer + Netvars::BaseEntity::m_iHealth);
        int team = memory.Read<int>(localPlayer + Netvars::BaseEntity::m_iTeamNum);
        
        std::cout << "[INFO] Local Player Health: " << std::dec << health << std::endl;
        std::cout << "[INFO] Local Player Team: " << team << std::endl;
        
        // Testar leitura de posição
        uintptr_t sceneNode = memory.Read<uintptr_t>(localPlayer + Netvars::BaseEntity::m_pGameSceneNode);
        if (sceneNode) {
            Vector3 position = memory.Read<Vector3>(sceneNode + Netvars::GameSceneNode::m_vecAbsOrigin);
            std::cout << "[INFO] Local Player Position: (" << position.x << ", " << position.y << ", " << position.z << ")" << std::endl;
        }
    }
    
    // Testar entity list
    uintptr_t entityList = memory.GetEntityList();
    std::cout << "[INFO] Entity List: 0x" << std::hex << entityList << std::endl;
    
    // Testar view matrix
    ViewMatrix viewMatrix = memory.GetViewMatrix();
    std::cout << "[INFO] View Matrix [0][0]: " << viewMatrix.matrix[0][0] << std::endl;
    
    // Testar screen size
    Vector2 screenSize = memory.GetScreenSize();
    std::cout << "[INFO] Screen Size: " << screenSize.x << "x" << screenSize.y << std::endl;
    
    // Testar detecção de players
    std::cout << "[INFO] Testando detecção de players..." << std::endl;
    std::vector<EntityInfo> players = memory.GetPlayers();
    std::cout << "[INFO] Players encontrados: " << players.size() << std::endl;
    
    for (size_t i = 0; i < players.size() && i < 5; i++) {
        const auto& player = players[i];
        std::cout << "[PLAYER " << i << "] ";
        std::cout << "Health: " << player.health;
        std::cout << ", Team: " << player.team;
        std::cout << ", Alive: " << (player.alive ? "Yes" : "No");
        std::cout << ", Distance: " << (int)player.distance << "m";
        if (!player.name.empty()) {
            std::cout << ", Name: " << player.name;
        }
        std::cout << std::endl;
    }
    
    memory.Shutdown();
    std::cout << "[TEST] Teste concluído!" << std::endl;
}

int main() {
    std::cout << "=== CS2 External Cheat - Test Memory ===" << std::endl;
    
    TestMemorySystem();
    
    std::cout << "\nPressione qualquer tecla para sair..." << std::endl;
    system("pause");
    return 0;
}
