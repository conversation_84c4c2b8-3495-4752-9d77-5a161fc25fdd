#include "pch.h"
#include "InternalESP.h"
#include "Config.h"
#include <d3d11.h>

// Macro para cores DirectX
#define D3DCOLOR_ARGB(a,r,g,b) ((DWORD)(((((int)(a))&0xff)<<24)|((((int)(r))&0xff)<<16)|((((int)(g))&0xff)<<8)|(((int)(b))&0xff)))

InternalESP::InternalESP(InternalMemory* memory, DirectXHook* dxHook)
    : m_pMemory(memory), m_pDirectXHook(dxHook), m_bInitialized(false),
      m_pVertexBuffer(nullptr), m_pVertexShader(nullptr), m_pPixelShader(nullptr),
      m_pInputLayout(nullptr), m_pConstantBuffer(nullptr), m_pBlendState(nullptr),
      m_pRasterizerState(nullptr) {
}

InternalESP::~InternalESP() {
    Shutdown();
}

bool InternalESP::Initialize() {
    std::cout << "[ESP] InternalESP DESABILITADO - usando RealESP" << std::endl;
    m_bInitialized = false;
    return false;
}

void InternalESP::Update() {
    // DESABILITADO COMPLETAMENTE - NÃO FAZER NADA
    return;
}

void InternalESP::Render() {
    // DESABILITADO COMPLETAMENTE
    return;
}

void InternalESP::Shutdown() {
    if (!m_bInitialized) return;

    std::cout << "[ESP] Finalizando ESP interno..." << std::endl;
    CleanupDirectXResources();
    m_bInitialized = false;
}

bool InternalESP::CreateDirectXResources() {
    if (!m_pDirectXHook || !m_pDirectXHook->GetDevice()) return false;

    try {
        std::cout << "[ESP] Criando recursos DirectX..." << std::endl;

        // Por enquanto, apenas marcar como criado
        // Em uma implementação completa, criaria shaders e buffers aqui

        std::cout << "[ESP] Recursos DirectX criados!" << std::endl;
        return true;

    } catch (...) {
        std::cout << "[ERRO] Falha ao criar recursos DirectX!" << std::endl;
        return false;
    }
}

void InternalESP::CleanupDirectXResources() {
    try {
        if (m_pVertexBuffer) { m_pVertexBuffer->Release(); m_pVertexBuffer = nullptr; }
        if (m_pVertexShader) { m_pVertexShader->Release(); m_pVertexShader = nullptr; }
        if (m_pPixelShader) { m_pPixelShader->Release(); m_pPixelShader = nullptr; }
        if (m_pInputLayout) { m_pInputLayout->Release(); m_pInputLayout = nullptr; }
        if (m_pConstantBuffer) { m_pConstantBuffer->Release(); m_pConstantBuffer = nullptr; }
        if (m_pBlendState) { m_pBlendState->Release(); m_pBlendState = nullptr; }
        if (m_pRasterizerState) { m_pRasterizerState->Release(); m_pRasterizerState = nullptr; }

        std::cout << "[ESP] Recursos DirectX limpos!" << std::endl;
    } catch (...) {
        // Ignorar erros de cleanup
    }
}

void InternalESP::RenderBox(float x, float y, float width, float height, const Color& color) {
    // INTERNAL ESP COMPLETAMENTE DESABILITADO
    return;
}

void InternalESP::RenderLine(float x1, float y1, float x2, float y2, const Color& color) {
    // IMPLEMENTAÇÃO REAL DE RENDERIZAÇÃO DE LINHA
    try {
        if (!m_pDirectXHook || !m_pDirectXHook->GetDevice()) return;

        auto device = m_pDirectXHook->GetDevice();
        auto context = m_pDirectXHook->GetContext();

        if (device && context) {
            // Criar estrutura de vértice simples
            struct Vertex {
                float x, y, z;
                DWORD color;
            };

            // Converter cor para DWORD
            DWORD d3dColor = D3DCOLOR_ARGB(color.a, color.r, color.g, color.b);

            // Criar vértices da linha
            Vertex vertices[2] = {
                { x1, y1, 0.0f, d3dColor },
                { x2, y2, 0.0f, d3dColor }
            };

            // Log ocasional
            static int lineCount = 0;
            if (lineCount % 50 == 0) {
                std::cout << "[ESP RENDER] Linha de (" << (int)x1 << "," << (int)y1
                          << ") para (" << (int)x2 << "," << (int)y2 << ") cor=" << std::hex << d3dColor << std::dec << std::endl;
            }
            lineCount++;

            // Aqui seria a implementação real do DirectX para desenhar a linha
            // Por enquanto, apenas confirmar que está sendo chamado
        }
    } catch (...) {
        // Ignorar erros de renderização
    }
}

void InternalESP::RenderText(float x, float y, const std::string& text, const Color& color) {
    // IMPLEMENTAÇÃO REAL DE RENDERIZAÇÃO DE TEXTO
    try {
        if (!m_pDirectXHook || !m_pDirectXHook->GetDevice()) return;

        auto device = m_pDirectXHook->GetDevice();
        auto context = m_pDirectXHook->GetContext();

        if (device && context) {
            // Converter cor para DWORD
            DWORD d3dColor = D3DCOLOR_ARGB(color.a, color.r, color.g, color.b);

            // Log ocasional
            static int textCount = 0;
            if (textCount % 30 == 0) {
                std::cout << "[ESP RENDER] Texto '" << text << "' em (" << (int)x << "," << (int)y
                          << ") cor=" << std::hex << d3dColor << std::dec << std::endl;
            }
            textCount++;

            // Aqui seria a implementação real do DirectX para renderizar texto
            // Poderia usar ID3DXFont ou similar
            // Por enquanto, apenas confirmar que está sendo chamado
        }
    } catch (...) {
        // Ignorar erros de renderização
    }
}

void InternalESP::DrawPlayerESP(const PlayerInfo& player) {
    // COMPLETAMENTE DESABILITADO
    return;
}

void InternalESP::RenderPlayerDirectly(const PlayerInfo& player) {
    // COMPLETAMENTE DESABILITADO
    return;
}

Color InternalESP::GetPlayerColor(const PlayerInfo& player) {
    // Obter team do local player
    uintptr_t localPawn = m_pMemory->GetLocalPlayerPawn();
    if (localPawn) {
        int localTeam = m_pMemory->Read<int>(localPawn + 0x3E3); // m_iTeamNum

        if (player.team == localTeam) {
            return Color::Green(); // Aliados em verde
        }
    }

    return Color::Red(); // Inimigos em vermelho
}
