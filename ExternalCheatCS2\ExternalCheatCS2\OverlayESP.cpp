#include "OverlayESP.h"
#include "Offsets.h"
#include <iostream>
#include <chrono>
#include <algorithm>

OverlayESP::OverlayESP(Memory* mem) : memory(mem), overlayWindow(nullptr), targetWindow(nullptr),
    overlayDC(nullptr), overlayBitmap(nullptr), memoryDC(nullptr),
    screenWidth(1920), screenHeight(1080),
    enemyColor(RGB(255, 0, 0)), teamColor(RGB(0, 255, 0)), textColor(RGB(255, 255, 255)),
    enabled(true), showBoxes(true), showNames(true), showHealth(true), 
    showDistance(true), teamCheck(true) {
}

OverlayESP::~OverlayESP() {
    Shutdown();
}

bool OverlayESP::Initialize() {
    std::cout << "[OverlayESP] Inicializando..." << std::endl;
    
    // Encontrar janela do CS2
    targetWindow = FindCS2Window();
    if (!targetWindow) {
        std::cout << "[ERRO] Janela do CS2 não encontrada!" << std::endl;
        return false;
    }
    
    // Obter tamanho da tela
    screenWidth = GetSystemMetrics(SM_CXSCREEN);
    screenHeight = GetSystemMetrics(SM_CYSCREEN);
    
    // Criar janela overlay
    if (!CreateOverlayWindow()) {
        std::cout << "[ERRO] Falha ao criar janela overlay!" << std::endl;
        return false;
    }
    
    // Inicializar gráficos
    if (!InitializeGraphics()) {
        std::cout << "[ERRO] Falha ao inicializar gráficos!" << std::endl;
        return false;
    }
    
    std::cout << "[OverlayESP] Inicializado com sucesso!" << std::endl;
    return true;
}

void OverlayESP::Shutdown() {
    CleanupGraphics();
    
    if (overlayWindow) {
        DestroyWindow(overlayWindow);
        overlayWindow = nullptr;
    }
}

HWND OverlayESP::FindCS2Window() {
    HWND hwnd = FindWindowW(nullptr, L"Counter-Strike 2");
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"cs2");
    }
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"CS2");
    }
    return hwnd;
}

bool OverlayESP::CreateOverlayWindow() {
    // Registrar classe da janela
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandleW(nullptr);
    wc.hCursor = LoadCursorW(nullptr, IDC_ARROW);
    wc.hbrBackground = nullptr;
    wc.lpszClassName = L"CS2OverlayClass";
    
    if (!RegisterClassExW(&wc)) {
        return false;
    }
    
    // Obter posição da janela alvo
    RECT targetRect;
    GetWindowRect(targetWindow, &targetRect);
    
    // Criar janela overlay transparente
    overlayWindow = CreateWindowExW(
        WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED | WS_EX_TOOLWINDOW,
        L"CS2OverlayClass",
        L"CS2 Overlay",
        WS_POPUP,
        targetRect.left,
        targetRect.top,
        targetRect.right - targetRect.left,
        targetRect.bottom - targetRect.top,
        nullptr,
        nullptr,
        GetModuleHandleW(nullptr),
        this
    );
    
    if (!overlayWindow) {
        return false;
    }
    
    // Configurar transparência
    SetLayeredWindowAttributes(overlayWindow, RGB(0, 0, 0), 0, LWA_COLORKEY);
    
    ShowWindow(overlayWindow, SW_SHOW);
    UpdateWindow(overlayWindow);
    
    return true;
}

bool OverlayESP::InitializeGraphics() {
    overlayDC = GetDC(overlayWindow);
    if (!overlayDC) {
        return false;
    }
    
    // Criar DC de memória para double buffering
    memoryDC = CreateCompatibleDC(overlayDC);
    if (!memoryDC) {
        return false;
    }
    
    // Criar bitmap para double buffering
    overlayBitmap = CreateCompatibleBitmap(overlayDC, screenWidth, screenHeight);
    if (!overlayBitmap) {
        return false;
    }
    
    SelectObject(memoryDC, overlayBitmap);
    
    return true;
}

void OverlayESP::CleanupGraphics() {
    if (overlayBitmap) {
        DeleteObject(overlayBitmap);
        overlayBitmap = nullptr;
    }
    
    if (memoryDC) {
        DeleteDC(memoryDC);
        memoryDC = nullptr;
    }
    
    if (overlayDC) {
        ReleaseDC(overlayWindow, overlayDC);
        overlayDC = nullptr;
    }
}

void OverlayESP::UpdateWindowPosition() {
    if (!targetWindow || !overlayWindow) return;
    
    RECT targetRect;
    if (GetWindowRect(targetWindow, &targetRect)) {
        SetWindowPos(
            overlayWindow,
            HWND_TOPMOST,
            targetRect.left,
            targetRect.top,
            targetRect.right - targetRect.left,
            targetRect.bottom - targetRect.top,
            SWP_NOACTIVATE
        );
    }
}

void OverlayESP::ClearOverlay() {
    // Limpar com cor transparente (preto)
    HBRUSH brush = CreateSolidBrush(RGB(0, 0, 0));
    RECT rect = { 0, 0, screenWidth, screenHeight };
    FillRect(memoryDC, &rect, brush);
    DeleteObject(brush);
}

void OverlayESP::DrawBox(int x, int y, int width, int height, COLORREF color) {
    HPEN pen = CreatePen(PS_SOLID, 2, color);
    HPEN oldPen = (HPEN)SelectObject(memoryDC, pen);
    
    // Desenhar retângulo
    MoveToEx(memoryDC, x, y, nullptr);
    LineTo(memoryDC, x + width, y);
    LineTo(memoryDC, x + width, y + height);
    LineTo(memoryDC, x, y + height);
    LineTo(memoryDC, x, y);
    
    SelectObject(memoryDC, oldPen);
    DeleteObject(pen);
}

void OverlayESP::DrawText(int x, int y, const std::string& text, COLORREF color) {
    SetTextColor(memoryDC, color);
    SetBkMode(memoryDC, TRANSPARENT);
    
    // Converter para wide string
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, nullptr, 0);
    std::wstring wideText(wideSize, 0);
    MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, &wideText[0], wideSize);
    
    TextOutW(memoryDC, x, y, wideText.c_str(), wideText.length() - 1);
}

void OverlayESP::Update() {
    // Atualizar posição da janela apenas a cada 60 frames para performance
    static int positionUpdateCounter = 0;
    if (positionUpdateCounter % 60 == 0) {
        UpdateWindowPosition();
    }
    positionUpdateCounter++;
}

void OverlayESP::Render() {
    // OVERLAY ESP DESABILITADO - USAR APENAS CHEAT INTERNO
    return;



    // Limpar overlay
    ClearOverlay();

    // Cache para otimização
    static uintptr_t cachedLocalPlayer = 0;
    static int cachedLocalTeam = 0;
    static int frameCount = 0;

    // Controle de FPS mais suave - renderizar a 60 FPS
    static auto lastRender = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastRender).count() < 16) {
        return; // Skip frame - 60 FPS para ESP suave
    }
    lastRender = now;

    // Atualizar cache a cada 60 frames para melhor performance
    if (frameCount % 60 == 0) {
        cachedLocalPlayer = memory->GetLocalPlayer();
        if (cachedLocalPlayer) {
            cachedLocalTeam = memory->Read<int>(cachedLocalPlayer + Netvars::BaseEntity::m_iTeamNum);
        }
    }
    frameCount++;

    // Obter lista de players apenas se necessário
    std::vector<EntityInfo> players = memory->GetPlayers();

    // Limitar número de players para performance (mais generoso)
    if (players.size() > 32) { // Máximo 32 players
        players.resize(32);
    }

    // Filtrar players por distância ANTES de processar
    players.erase(std::remove_if(players.begin(), players.end(),
        [](const EntityInfo& player) {
            return player.distance > g_Config.esp.maxDistance || player.distance <= 0;
        }), players.end());

    // Cache de configurações para evitar acessos repetidos
    bool showTeammates = g_Config.esp.showTeammates;
    bool showBoxes = g_Config.esp.showBoxes;
    bool showNames = g_Config.esp.showNames;
    bool showHealth = g_Config.esp.showHealth;
    bool showDistance = g_Config.esp.showDistance;

    // Renderizar cada player (já filtrados por distância)
    for (const auto& player : players) {
        // Verificações rápidas primeiro
        if (!player.onScreen) continue;
        if (player.team == cachedLocalTeam && !showTeammates) continue;

        Vector2 screenPos = player.screenPos;
        Vector2 headScreenPos = player.headScreenPos;

        // Verificação simplificada de bounds
        if (screenPos.x < -50 || screenPos.x > screenWidth + 50 ||
            screenPos.y < -50 || screenPos.y > screenHeight + 50) {
            continue;
        }

        // Escolher cor
        COLORREF color = (player.team != cachedLocalTeam) ? enemyColor : teamColor;

        // OVERLAY ESP COMPLETAMENTE DESABILITADO
        continue;

        // Remover weapon por enquanto - não implementado na EntityInfo
        // if (g_Config.esp.showWeapons && !player.weapon.empty()) {
        //     DrawText(textX, textY, player.weapon, textColor);
        // }
    }

    // Copiar buffer para tela apenas se houver mudanças
    if (!players.empty()) {
        BitBlt(overlayDC, 0, 0, screenWidth, screenHeight, memoryDC, 0, 0, SRCCOPY);
    }
}

LRESULT CALLBACK OverlayESP::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        case WM_PAINT: {
            PAINTSTRUCT ps;
            BeginPaint(hwnd, &ps);
            EndPaint(hwnd, &ps);
            return 0;
        }
    }
    
    return DefWindowProcW(hwnd, uMsg, wParam, lParam);
}
