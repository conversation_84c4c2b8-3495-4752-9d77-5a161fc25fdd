#pragma once

#include "Memory.h"
#include <Windows.h>
#include <vector>

class SimpleESP {
private:
    Memory* memory;
    HWND targetWindow;
    HDC targetDC;
    HDC memoryDC;
    HBITMAP memoryBitmap;
    int screenWidth;
    int screenHeight;
    
    // Cores
    COLORREF enemyColor;
    COLORREF teamColor;
    COLORREF textColor;
    
    // Configurações
    bool enabled;
    bool showBoxes;
    bool showNames;
    bool showHealth;
    bool showDistance;
    bool teamCheck;
    
    // Funções auxiliares
    bool WorldToScreen(const Vector3& worldPos, Vector2& screenPos);
    void DrawBox(int x, int y, int width, int height, COLORREF color);
    void DrawText(int x, int y, const std::string& text, COLORREF color);
    void DrawLine(int x1, int y1, int x2, int y2, COLORREF color);
    HWND FindCS2Window();
    
public:
    SimpleESP(Memory* mem);
    ~SimpleESP();
    
    bool Initialize();
    void Shutdown();
    void Update();
    void Render();
    
    // Configurações
    void SetEnabled(bool enable) { enabled = enable; }
    bool IsEnabled() const { return enabled; }
    
    void SetShowBoxes(bool show) { showBoxes = show; }
    void SetShowNames(bool show) { showNames = show; }
    void SetShowHealth(bool show) { showHealth = show; }
    void SetShowDistance(bool show) { showDistance = show; }
    void SetTeamCheck(bool check) { teamCheck = check; }
};
