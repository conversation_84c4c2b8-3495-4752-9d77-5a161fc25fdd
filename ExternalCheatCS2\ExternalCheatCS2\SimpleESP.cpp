#include "SimpleESP.h"
#include "Offsets.h"
#include <iostream>
#include <string>

SimpleESP::SimpleESP(Memory* mem) : memory(mem), targetWindow(nullptr), targetDC(nullptr),
    memoryDC(nullptr), memoryBitmap(nullptr), screenWidth(1920), screenHeight(1080),
    enemyColor(RGB(255, 0, 0)), teamColor(RGB(0, 255, 0)), textColor(RGB(255, 255, 255)),
    enabled(true), showBoxes(true), showNames(true), showHealth(true), 
    showDistance(true), teamCheck(true) {
}

SimpleESP::~SimpleESP() {
    Shutdown();
}

bool SimpleESP::Initialize() {
    std::cout << "[SimpleESP] Inicializando overlay..." << std::endl;

    // Encontrar janela do CS2
    targetWindow = FindCS2Window();
    if (!targetWindow) {
        std::cout << "[ERRO] Janela do CS2 não encontrada!" << std::endl;
        return false;
    }

    // Obter tamanho da tela
    screenWidth = GetSystemMetrics(SM_CXSCREEN);
    screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // Usar DC da tela inteira para desenhar overlay
    targetDC = GetDC(nullptr); // DC da tela inteira
    if (!targetDC) {
        std::cout << "[ERRO] Falha ao obter DC da tela!" << std::endl;
        return false;
    }

    std::cout << "[SimpleESP] Overlay inicializado com sucesso!" << std::endl;
    std::cout << "[SimpleESP] Tamanho da tela: " << screenWidth << "x" << screenHeight << std::endl;
    return true;
}

void SimpleESP::Shutdown() {
    if (targetDC) {
        ReleaseDC(nullptr, targetDC); // Liberar DC da tela
        targetDC = nullptr;
    }
}

HWND SimpleESP::FindCS2Window() {
    // Procurar por diferentes nomes de janela do CS2
    HWND hwnd = FindWindowW(nullptr, L"Counter-Strike 2");
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"cs2");
    }
    if (!hwnd) {
        hwnd = FindWindowW(nullptr, L"CS2");
    }
    
    return hwnd;
}

bool SimpleESP::WorldToScreen(const Vector3& worldPos, Vector2& screenPos) {
    ViewMatrix viewMatrix = memory->GetViewMatrix();
    
    float w = viewMatrix.matrix[3][0] * worldPos.x + viewMatrix.matrix[3][1] * worldPos.y + 
              viewMatrix.matrix[3][2] * worldPos.z + viewMatrix.matrix[3][3];
    
    if (w < 0.001f) {
        return false;
    }
    
    float x = viewMatrix.matrix[0][0] * worldPos.x + viewMatrix.matrix[0][1] * worldPos.y + 
              viewMatrix.matrix[0][2] * worldPos.z + viewMatrix.matrix[0][3];
    float y = viewMatrix.matrix[1][0] * worldPos.x + viewMatrix.matrix[1][1] * worldPos.y + 
              viewMatrix.matrix[1][2] * worldPos.z + viewMatrix.matrix[1][3];
    
    float invW = 1.0f / w;
    x *= invW;
    y *= invW;
    
    screenPos.x = (screenWidth * 0.5f) + (x * screenWidth * 0.5f);
    screenPos.y = (screenHeight * 0.5f) - (y * screenHeight * 0.5f);
    
    return true;
}

void SimpleESP::DrawBox(int x, int y, int width, int height, COLORREF color) {
    // Desenhar borda preta primeiro para contraste
    HPEN blackPen = CreatePen(PS_SOLID, 4, RGB(0, 0, 0));
    HPEN oldPen = (HPEN)SelectObject(targetDC, blackPen);

    MoveToEx(targetDC, x, y, nullptr);
    LineTo(targetDC, x + width, y);
    LineTo(targetDC, x + width, y + height);
    LineTo(targetDC, x, y + height);
    LineTo(targetDC, x, y);

    SelectObject(targetDC, oldPen);
    DeleteObject(blackPen);

    // Desenhar borda colorida por cima
    HPEN colorPen = CreatePen(PS_SOLID, 2, color);
    oldPen = (HPEN)SelectObject(targetDC, colorPen);

    MoveToEx(targetDC, x, y, nullptr);
    LineTo(targetDC, x + width, y);
    LineTo(targetDC, x + width, y + height);
    LineTo(targetDC, x, y + height);
    LineTo(targetDC, x, y);

    SelectObject(targetDC, oldPen);
    DeleteObject(colorPen);
}

void SimpleESP::DrawText(int x, int y, const std::string& text, COLORREF color) {
    // Converter string para wide string
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, nullptr, 0);
    std::wstring wideText(wideSize, 0);
    MultiByteToWideChar(CP_UTF8, 0, text.c_str(), -1, &wideText[0], wideSize);

    // Desenhar sombra preta primeiro
    SetTextColor(targetDC, RGB(0, 0, 0));
    SetBkMode(targetDC, TRANSPARENT);
    TextOutW(targetDC, x + 1, y + 1, wideText.c_str(), wideText.length() - 1);

    // Desenhar texto colorido por cima
    SetTextColor(targetDC, color);
    TextOutW(targetDC, x, y, wideText.c_str(), wideText.length() - 1);
}

void SimpleESP::DrawLine(int x1, int y1, int x2, int y2, COLORREF color) {
    HPEN pen = CreatePen(PS_SOLID, 1, color);
    HPEN oldPen = (HPEN)SelectObject(targetDC, pen);
    
    MoveToEx(targetDC, x1, y1, nullptr);
    LineTo(targetDC, x2, y2);
    
    SelectObject(targetDC, oldPen);
    DeleteObject(pen);
}

void SimpleESP::Update() {
    // Atualização é feita no Render()
}

void SimpleESP::Render() {
    // SIMPLE ESP DESABILITADO - USAR APENAS CHEAT INTERNO
    return;

    // Obter informações do local player
    uintptr_t localPlayer = memory->GetLocalPlayer();
    int localTeam = 0;

    if (localPlayer) {
        localTeam = memory->Read<int>(localPlayer + Netvars::BaseEntity::m_iTeamNum);
    }

    // Obter lista de players
    std::vector<EntityInfo> players = memory->GetPlayers();

    // Renderizar cada player
    for (const auto& player : players) {
        // Team check
        if (teamCheck && player.team == localTeam && localTeam != 0) {
            continue;
        }

        // Usar posições já calculadas do world-to-screen
        if (!player.onScreen) {
            continue;
        }

        Vector2 screenPos = player.screenPos;
        Vector2 headScreenPos = player.headScreenPos;

        // Verificar se está na tela
        if (screenPos.x < 0 || screenPos.x > screenWidth ||
            screenPos.y < 0 || screenPos.y > screenHeight) {
            continue;
        }

        // Escolher cor
        COLORREF color = (player.team != localTeam) ? enemyColor : teamColor;

        // Calcular box usando posição dos pés e cabeça
        float boxHeight = abs(screenPos.y - headScreenPos.y);
        float boxWidth = boxHeight * 0.4f;

        // Garantir tamanhos mínimos
        if (boxHeight < 20) boxHeight = 20;
        if (boxWidth < 10) boxWidth = 10;

        // Limitar tamanhos máximos
        if (boxHeight > 150) boxHeight = 150;
        if (boxWidth > 60) boxWidth = 60;

        // Posição da box (centrada horizontalmente, pés na base)
        int boxX = (int)(screenPos.x - boxWidth / 2);
        int boxY = (int)(screenPos.y - boxHeight);

        // Desenhar box apenas se estiver visível
        if (showBoxes) {
            DrawBox(boxX, boxY, (int)boxWidth, (int)boxHeight, color);
        }

        // Desenhar informações acima da box
        int textY = boxY - 20;
        int textX = boxX;

        if (showDistance) {
            std::string distText = std::to_string((int)player.distance) + "m";
            DrawText(textX, textY, distText, textColor);
            textY -= 18;
        }

        if (showHealth) {
            std::string healthText = "HP: " + std::to_string(player.health);
            DrawText(textX, textY, healthText, textColor);
            textY -= 18;
        }

        if (showNames && !player.name.empty()) {
            DrawText(textX, textY, player.name, textColor);
        }

        // Pequena pausa para reduzir flickering
        Sleep(1);
    }
}
