#include "Memory.h"
#include "Offsets.h"
#include <iostream>

Memory::Memory() : processHandle(nullptr), processId(0), clientBase(0), engine2Base(0) {
}

Memory::~Memory() {
    Shutdown();
}

bool Memory::Initialize() {
    std::wcout << L"[Memory] Procurando processo cs2.exe..." << std::endl;
    
    processId = GetProcessId(L"cs2.exe");
    if (processId == 0) {
        std::wcout << L"[ERRO] Processo cs2.exe não encontrado!" << std::endl;
        return false;
    }
    
    std::wcout << L"[Memory] Processo encontrado! PID: " << processId << std::endl;
    
    processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!processHandle) {
        std::wcout << L"[ERRO] Falha ao abrir handle do processo!" << std::endl;
        return false;
    }
    
    std::wcout << L"[Memory] Handle do processo obtido com sucesso!" << std::endl;
    
    // Obter base addresses das DLLs
    clientBase = GetModuleBase(processId, L"client.dll");
    if (!clientBase) {
        std::wcout << L"[ERRO] Falha ao obter base address da client.dll!" << std::endl;
        return false;
    }
    
    engine2Base = GetModuleBase(processId, L"engine2.dll");
    if (!engine2Base) {
        std::wcout << L"[ERRO] Falha ao obter base address da engine2.dll!" << std::endl;
        return false;
    }
    
    std::wcout << L"[Memory] client.dll base: 0x" << std::hex << clientBase << std::endl;
    std::wcout << L"[Memory] engine2.dll base: 0x" << std::hex << engine2Base << std::endl;
    
    return true;
}

void Memory::Shutdown() {
    if (processHandle) {
        CloseHandle(processHandle);
        processHandle = nullptr;
    }
}

DWORD Memory::GetProcessId(const std::wstring& processName) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    PROCESSENTRY32W entry = {};
    entry.dwSize = sizeof(entry);
    
    if (Process32FirstW(snapshot, &entry)) {
        do {
            if (processName == entry.szExeFile) {
                CloseHandle(snapshot);
                return entry.th32ProcessID;
            }
        } while (Process32NextW(snapshot, &entry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

uintptr_t Memory::GetModuleBase(DWORD processId, const std::wstring& moduleName) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, processId);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    MODULEENTRY32W entry = {};
    entry.dwSize = sizeof(entry);
    
    if (Module32FirstW(snapshot, &entry)) {
        do {
            if (moduleName == entry.szModule) {
                CloseHandle(snapshot);
                return reinterpret_cast<uintptr_t>(entry.modBaseAddr);
            }
        } while (Module32NextW(snapshot, &entry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

std::string Memory::ReadString(uintptr_t address, size_t maxLength) {
    std::vector<char> buffer(maxLength);
    SIZE_T bytesRead;
    
    if (ReadProcessMemory(processHandle, (LPCVOID)address, buffer.data(), maxLength - 1, &bytesRead)) {
        buffer[bytesRead] = '\0';
        return std::string(buffer.data());
    }
    
    return "";
}

uintptr_t Memory::GetLocalPlayer() {
    return Read<uintptr_t>(clientBase + Offsets::Client::dwLocalPlayerPawn);
}

uintptr_t Memory::GetLocalPlayerController() {
    return Read<uintptr_t>(clientBase + Offsets::Client::dwLocalPlayerController);
}

uintptr_t Memory::GetEntityList() {
    return Read<uintptr_t>(clientBase + Offsets::Client::dwEntityList);
}

ViewMatrix Memory::GetViewMatrix() {
    return Read<ViewMatrix>(clientBase + Offsets::Client::dwViewMatrix);
}

Vector2 Memory::GetScreenSize() {
    // Tentar obter tamanho da tela do Windows diretamente
    int width = GetSystemMetrics(SM_CXSCREEN);
    int height = GetSystemMetrics(SM_CYSCREEN);

    // Se não conseguir, usar valores padrão
    if (width <= 0 || height <= 0) {
        width = 1920;
        height = 1080;
    }

    return Vector2((float)width, (float)height);
}

bool Memory::IsValidEntity(uintptr_t entityAddress) {
    if (!entityAddress) return false;

    // Verificar se a entidade tem um scene node válido
    uintptr_t sceneNode = Read<uintptr_t>(entityAddress + Netvars::BaseEntity::m_pGameSceneNode);
    if (!sceneNode) return false;

    // Verificar se não está dormant
    bool dormant = Read<bool>(sceneNode + Netvars::GameSceneNode::m_bDormant);
    if (dormant) return false;

    return true;
}

bool Memory::WorldToScreen(const Vector3& worldPos, Vector2& screenPos, const ViewMatrix& viewMatrix, const Vector2& screenSize) {
    float w = viewMatrix.matrix[3][0] * worldPos.x + viewMatrix.matrix[3][1] * worldPos.y +
              viewMatrix.matrix[3][2] * worldPos.z + viewMatrix.matrix[3][3];

    if (w < 0.001f) {
        return false;
    }

    float x = viewMatrix.matrix[0][0] * worldPos.x + viewMatrix.matrix[0][1] * worldPos.y +
              viewMatrix.matrix[0][2] * worldPos.z + viewMatrix.matrix[0][3];
    float y = viewMatrix.matrix[1][0] * worldPos.x + viewMatrix.matrix[1][1] * worldPos.y +
              viewMatrix.matrix[1][2] * worldPos.z + viewMatrix.matrix[1][3];

    float invW = 1.0f / w;
    x *= invW;
    y *= invW;

    screenPos.x = (screenSize.x * 0.5f) + (x * screenSize.x * 0.5f);
    screenPos.y = (screenSize.y * 0.5f) - (y * screenSize.y * 0.5f);

    return true;
}

std::vector<EntityInfo> Memory::GetPlayers() {
    std::vector<EntityInfo> players;

    uintptr_t localPlayer = GetLocalPlayer();
    ViewMatrix viewMatrix = GetViewMatrix();
    Vector2 screenSize = GetScreenSize();

    Vector3 localPos = {};
    if (localPlayer) {
        uintptr_t localSceneNode = Read<uintptr_t>(localPlayer + Netvars::BaseEntity::m_pGameSceneNode);
        if (localSceneNode) {
            localPos = Read<Vector3>(localSceneNode + Netvars::GameSceneNode::m_vecAbsOrigin);
        }
    }

    // Usar o endereço direto da EntityList que contém ponteiros para C_CSPlayerPawn
    uintptr_t entityListAddress = clientBase + Offsets::Client::dwEntityList;

    // Iterar através dos ponteiros na EntityList
    for (int i = 0; i < 64; i++) {
        // Ler ponteiro direto para C_CSPlayerPawn
        uintptr_t playerPawn = Read<uintptr_t>(entityListAddress + (i * 8));

        // Pular se for nulo ou o local player
        if (!playerPawn || playerPawn == localPlayer) continue;

        // Verificar se é um player válido
        int health = Read<int>(playerPawn + Netvars::BaseEntity::m_iHealth);
        int team = Read<int>(playerPawn + Netvars::BaseEntity::m_iTeamNum);
        int lifeState = Read<int>(playerPawn + Netvars::BaseEntity::m_lifeState);

        // Verificar se os valores fazem sentido para um player
        // LifeState parece ser diferente do esperado, vamos aceitar valores razoáveis
        if (health > 0 && health <= 100 &&
            (team == Constants::TEAM_TERRORIST || team == Constants::TEAM_COUNTER_TERRORIST)) {

            // Verificar se tem scene node válido
            uintptr_t sceneNode = Read<uintptr_t>(playerPawn + Netvars::BaseEntity::m_pGameSceneNode);
            if (!sceneNode) continue;

            Vector3 position = Read<Vector3>(sceneNode + Netvars::GameSceneNode::m_vecAbsOrigin);

            // Verificar se a posição é razoável
            float distance = localPos.Distance(position);
            if (distance > 5000.0f || distance < 1.0f) continue;

            EntityInfo info = GetEntityInfo(playerPawn, i);

            if (info.alive && info.team != 0) {
                info.distance = distance;
                info.onScreen = WorldToScreen(info.position, info.screenPos, viewMatrix, screenSize);

                Vector3 headPos = info.position;
                headPos.z += Constants::PLAYER_HEIGHT;
                WorldToScreen(headPos, info.headScreenPos, viewMatrix, screenSize);

                players.push_back(info);
            }
        }
    }

    return players;
}

EntityInfo Memory::GetEntityInfo(uintptr_t entityAddress, int index) {
    EntityInfo info = {};
    info.address = entityAddress;

    if (!IsValidEntity(entityAddress)) {
        return info;
    }

    // Obter scene node
    uintptr_t sceneNode = Read<uintptr_t>(entityAddress + Netvars::BaseEntity::m_pGameSceneNode);
    info.sceneNode = sceneNode;

    if (sceneNode) {
        // Posição
        info.position = Read<Vector3>(sceneNode + Netvars::GameSceneNode::m_vecAbsOrigin);
        info.dormant = Read<bool>(sceneNode + Netvars::GameSceneNode::m_bDormant);
    }

    // Informações básicas da entidade
    info.health = Read<int>(entityAddress + Netvars::BaseEntity::m_iHealth);
    info.team = Read<int>(entityAddress + Netvars::BaseEntity::m_iTeamNum);

    // Life state - assumir vivo se health > 0
    int lifeState = Read<int>(entityAddress + Netvars::BaseEntity::m_lifeState);
    info.alive = (info.health > 0); // Usar health como indicador principal

    // Spotted
    info.spotted = Read<bool>(entityAddress + Netvars::CSPlayerPawn::m_bSpotted);

    // Se temos um controller, obter informações adicionais
    if (info.controller) {
        // Nome do jogador
        uintptr_t namePtr = Read<uintptr_t>(info.controller + Netvars::BasePlayerController::m_sSanitizedPlayerName);
        if (namePtr) {
            info.name = ReadString(namePtr, 64);
        }

        // Health e armor do controller (mais confiável)
        int controllerHealth = Read<int>(info.controller + Netvars::BasePlayerController::m_iPawnHealth);
        int controllerArmor = Read<int>(info.controller + Netvars::BasePlayerController::m_iPawnArmor);
        bool controllerAlive = Read<bool>(info.controller + Netvars::BasePlayerController::m_bPawnIsAlive);

        if (controllerHealth > 0) info.health = controllerHealth;
        info.armor = controllerArmor;
        if (!controllerAlive) info.alive = false;
    }

    return info;
}
