#pragma once
#include <Windows.h>

struct ESPConfig {
    // ESP Geral
    bool enabled = true;
    bool showBoxes = true;
    bool showNames = true;
    bool showHealth = true;
    bool showDistance = true;
    bool showWeapons = false;
    bool showTeammates = false;
    
    // Cores (RGBA)
    float enemyColor[4] = { 1.0f, 0.0f, 0.0f, 1.0f };    // Vermelho
    float teammateColor[4] = { 0.0f, 1.0f, 0.0f, 1.0f }; // Verde
    float boxColor[4] = { 1.0f, 1.0f, 1.0f, 1.0f };      // Branco
    float nameColor[4] = { 1.0f, 1.0f, 0.0f, 1.0f };     // Amarelo
    
    // Configurações visuais
    float boxThickness = 2.0f;
    float maxDistance = 1000.0f;
    int fontSize = 14;
    
    // Configurações de performance
    int updateRate = 60; // FPS
    bool vsync = false;
};

struct CheatConfig {
    ESPConfig esp;
    
    // Configurações gerais
    bool showMenu = false;
    int menuKey = VK_INSERT; // Tecla Insert para abrir menu
    
    // Configurações de segurança
    bool panicMode = false;
    int panicKey = VK_END; // Tecla End para panic
};

extern CheatConfig g_Config;
