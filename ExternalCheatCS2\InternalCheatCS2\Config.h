#pragma once
#include "pch.h"

struct ESPConfig {
    bool enabled = true;
    bool showBoxes = true;
    bool showNames = true;
    bool showHealth = true;
    bool showDistance = true;
    bool showTeammates = false;
    float maxDistance = 1000.0f;
    
    // Cores
    Color enemyColor = Color::Red();
    Color teamColor = Color::Green();
    Color textColor = Color::White();
};

struct AimbotConfig {
    bool enabled = false;
    float fov = 60.0f;
    float smoothing = 1.0f;
    bool visibleOnly = true;
    int targetBone = 6; // Head
};

struct MiscConfig {
    bool noFlash = false;
    bool noSmoke = false;
    bool bunnyHop = false;
    bool triggerBot = false;
};

class Config {
public:
    ESPConfig esp;
    AimbotConfig aimbot;
    MiscConfig misc;
    
    Config() {
        LoadDefaults();
    }
    
    void LoadDefaults() {
        // ESP
        esp.enabled = true;
        esp.showBoxes = true;
        esp.showNames = true;
        esp.showHealth = true;
        esp.showDistance = true;
        esp.showTeammates = false;
        esp.maxDistance = 1000.0f;
        esp.enemyColor = Color::Red();
        esp.teamColor = Color::Green();
        esp.textColor = Color::White();
        
        // Aimbot
        aimbot.enabled = false;
        aimbot.fov = 60.0f;
        aimbot.smoothing = 1.0f;
        aimbot.visibleOnly = true;
        aimbot.targetBone = 6;
        
        // Misc
        misc.noFlash = false;
        misc.noSmoke = false;
        misc.bunnyHop = false;
        misc.triggerBot = false;
    }
    
    void Save(const std::string& filename = "config.ini") {
        // Implementação futura
    }
    
    void Load(const std::string& filename = "config.ini") {
        // Implementação futura
    }
};

// Instância global
extern Config g_Config;
