#include "pch.h"
#include "InternalMemory.h"

InternalMemory::InternalMemory() 
    : m_moduleBase(0), m_bInitialized(false), m_localPlayerController(0), m_localPlayerPawn(0) {
    memset(&m_viewMatrix, 0, sizeof(m_viewMatrix));
}

InternalMemory::~InternalMemory() {
    Shutdown();
}

bool InternalMemory::Initialize() {
    try {
        std::cout << "[MEMORY] Inicializando memória interna..." << std::endl;
        
        // Obter base do módulo client.dll
        m_moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandleA("client.dll"));
        if (!m_moduleBase) {
            std::cout << "[ERRO] Não foi possível obter base do client.dll!" << std::endl;
            return false;
        }
        
        std::cout << "[MEMORY] Base client.dll: 0x" << std::hex << m_moduleBase << std::dec << std::endl;
        
        // Atualizar cache inicial
        UpdateCache();
        
        m_bInitialized = true;
        std::cout << "[MEMORY] Memória interna inicializada!" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[ERRO] Exceção em InternalMemory::Initialize: " << e.what() << std::endl;
        return false;
    }
}

void InternalMemory::Shutdown() {
    if (!m_bInitialized) return;
    
    std::cout << "[MEMORY] Finalizando memória interna..." << std::endl;
    m_bInitialized = false;
}

bool InternalMemory::UpdateCache() {
    if (!m_moduleBase) return false;
    
    try {
        // Verificar se moduleBase é válido
        if (!m_moduleBase || m_moduleBase < 0x10000) {
            std::cout << "[ERRO] ModuleBase inválido!" << std::endl;
            return false;
        }

        // Atualizar local player controller com verificação
        uintptr_t localControllerAddr = m_moduleBase + Offsets::Client::dwLocalPlayerController;
        if (localControllerAddr > m_moduleBase && localControllerAddr < m_moduleBase + 0x10000000) {
            m_localPlayerController = Read<uintptr_t>(localControllerAddr);
        }

        // Atualizar local player pawn com verificação
        uintptr_t localPawnAddr = m_moduleBase + Offsets::Client::dwLocalPlayerPawn;
        if (localPawnAddr > m_moduleBase && localPawnAddr < m_moduleBase + 0x10000000) {
            m_localPlayerPawn = Read<uintptr_t>(localPawnAddr);
        }

        // USAR MÚLTIPLOS ENDEREÇOS CONHECIDOS DA VIEWMATRIX
        uintptr_t viewMatrixCandidates[] = {
            m_moduleBase + 0x1A6D280,  // Offset original
            m_moduleBase + 0x1A6D000,  // Offset próximo
            m_moduleBase + 0x1A6E000,  // Offset alternativo
            m_moduleBase + 0x1A70000,  // Offset alternativo 2
            0x7FF8AE11D280,            // Endereço absoluto do Cheat Engine
            0
        };

        uintptr_t viewMatrixAddr = 0;

        // TESTAR TODOS OS ENDEREÇOS CANDIDATOS
        static int matrixDebug = 0;
        bool foundValidMatrix = false;

        for (int i = 0; viewMatrixCandidates[i] != 0; i++) {
            uintptr_t testAddr = viewMatrixCandidates[i];

            // Debug desabilitado para reduzir spam
            // if (matrixDebug % 200 == 0) {
            //     std::cout << "[MATRIX TEST] Testando endereço " << i << ": 0x" << std::hex << testAddr << std::dec << std::endl;
            // }

            if (testAddr > 0x10000 && testAddr < 0x7FFFFFFFFFFF) {
                try {
                    // TESTAR LEITURA DIRETA DESTE ENDEREÇO
                    float matrixData[16];
                    if (!IsBadReadPtr(reinterpret_cast<void*>(testAddr), sizeof(matrixData))) {
                        memcpy(matrixData, reinterpret_cast<void*>(testAddr), sizeof(matrixData));

                        // VERIFICAR SE É UMA VIEWMATRIX VÁLIDA
                        bool validMatrix = true;
                        int nonZeroCount = 0;

                        for (int j = 0; j < 16; j++) {
                            if (isnan(matrixData[j]) || isinf(matrixData[j])) {
                                validMatrix = false;
                                break;
                            }
                            if (matrixData[j] != 0.0f) nonZeroCount++;
                        }

                        // ACEITAR SE TEM VALORES NÃO-ZERO E VÁLIDOS
                        if (validMatrix && nonZeroCount >= 8) {
                            // ESTA É UMA VIEWMATRIX VÁLIDA!
                            memcpy(&m_viewMatrix, matrixData, sizeof(Matrix4x4));
                            foundValidMatrix = true;

                            // Debug desabilitado para reduzir spam
                            // if (matrixDebug % 200 == 0) {
                            //     std::cout << "[MATRIX SUCCESS] ViewMatrix válida encontrada em 0x" << std::hex << testAddr
                            //               << " [3][3]=" << std::dec << m_viewMatrix.m[3][3]
                            //               << " [0][0]=" << m_viewMatrix.m[0][0]
                            //               << " NonZero=" << nonZeroCount << std::endl;
                            // }
                            break; // Sair do loop, encontramos uma matriz válida
                        } else if (matrixDebug % 200 == 0) {
                            std::cout << "[MATRIX REJECT] Endereço " << i << " rejeitado: NonZero=" << nonZeroCount
                                      << " Valid=" << validMatrix << std::endl;
                        }
                    }
                } catch (...) {
                    if (matrixDebug % 200 == 0) {
                        std::cout << "[MATRIX ERROR] Erro ao ler endereço " << i << std::endl;
                    }
                }
            }
        }

        // Se não encontrou matriz válida, usar padrão
        if (!foundValidMatrix) {
            if (matrixDebug % 200 == 0) {
                std::cout << "[MATRIX FALLBACK] Usando ViewMatrix padrão" << std::endl;
            }

            // Matriz identidade como fallback
            memset(&m_viewMatrix, 0, sizeof(Matrix4x4));
            m_viewMatrix.m[0][0] = 1.0f;
            m_viewMatrix.m[1][1] = 1.0f;
            m_viewMatrix.m[2][2] = 1.0f;
            m_viewMatrix.m[3][3] = 1.0f;
        }

        matrixDebug++;

        return true;

    } catch (...) {
        std::cout << "[ERRO] Exceção em UpdateCache!" << std::endl;
        return false;
    }
}

uintptr_t InternalMemory::GetLocalPlayerController() {
    UpdateCache();
    return m_localPlayerController;
}

uintptr_t InternalMemory::GetLocalPlayerPawn() {
    UpdateCache();
    return m_localPlayerPawn;
}

Matrix4x4 InternalMemory::GetViewMatrix() {
    UpdateCache();
    return m_viewMatrix;
}

std::vector<PlayerInfo> InternalMemory::GetPlayers() {
    std::vector<PlayerInfo> players;

    if (!m_moduleBase) {
        std::cout << "[MEMORY] Erro: moduleBase é null!" << std::endl;
        return players;
    }

    try {
        uintptr_t entityList = m_moduleBase + Offsets::Client::dwEntityList;
        uintptr_t localPawn = GetLocalPlayerPawn();

        // Debug desabilitado para reduzir spam
        // static int debugCounter = 0;
        // if (debugCounter % 100 == 0) { // Debug ocasional
        //     std::cout << "[MEMORY] EntityList: 0x" << std::hex << entityList << std::dec
        //               << " LocalPawn: 0x" << std::hex << localPawn << std::dec << std::endl;
        // }
        // debugCounter++;

        int validEntities = 0;

        // Obter posição do local player usando leitura direta (que funciona)
        Vector3 localPos = {};
        if (localPawn) {
            try {
                uintptr_t localSceneNodeAddr = localPawn + 0x328;
                if (!IsBadReadPtr(reinterpret_cast<void*>(localSceneNodeAddr), sizeof(uintptr_t))) {
                    uintptr_t localSceneNode = *reinterpret_cast<uintptr_t*>(localSceneNodeAddr);
                    if (localSceneNode && localSceneNode > 0x10000) {
                        uintptr_t localPosAddr = localSceneNode + 0xD0;
                        if (!IsBadReadPtr(reinterpret_cast<void*>(localPosAddr), sizeof(Vector3))) {
                            localPos = *reinterpret_cast<Vector3*>(localPosAddr);
                        }
                    }
                }
            } catch (...) {
                // Se falhar, usar posição zero
            }
        }

        // USAR DADOS EXATOS DO CHEAT ENGINE!
        // Debug removido para performance

        // DETECÇÃO RIGOROSA: Apenas primeiros 20 slots (suficiente para 10v10)
        for (int i = 0; i < 20; i++) {
            // Usar offset correto da EntityList (0x10 por entidade)
            uintptr_t entityAddress = entityList + (i * 0x10);
            if (entityAddress < m_moduleBase || entityAddress > m_moduleBase + 0x20000000) {
                continue;
            }

            // Ler ponteiro com verificação básica
            uintptr_t playerPawnPtr = 0;
            try {
                if (!IsBadReadPtr(reinterpret_cast<void*>(entityAddress), sizeof(uintptr_t))) {
                    playerPawnPtr = *reinterpret_cast<uintptr_t*>(entityAddress);
                }
            } catch (...) {
                continue;
            }

            // Pular local player e entidades inválidas
            if (!playerPawnPtr || playerPawnPtr < 0x10000 || playerPawnPtr == localPawn) continue;

            // Debug removido para performance

            // LEITURA SEGURA DOS DADOS DO PLAYER
            int foundHP = 0;
            int foundTeam = 0;
            int lifeState = 0;

            try {
                // Verificar se os endereços são válidos antes de ler
                uintptr_t hpAddr = playerPawnPtr + 0x344;
                uintptr_t teamAddr = playerPawnPtr + 0x3E3;
                uintptr_t lifeAddr = playerPawnPtr + 0x348;

                if (!IsBadReadPtr(reinterpret_cast<void*>(hpAddr), sizeof(int))) {
                    foundHP = *reinterpret_cast<int*>(hpAddr);
                }
                if (!IsBadReadPtr(reinterpret_cast<void*>(teamAddr), sizeof(int))) {
                    foundTeam = *reinterpret_cast<int*>(teamAddr);
                }
                if (!IsBadReadPtr(reinterpret_cast<void*>(lifeAddr), sizeof(int))) {
                    lifeState = *reinterpret_cast<int*>(lifeAddr);
                }
            } catch (...) {
                // Se falhar, pular este player
                continue;
            }

            // CORREÇÃO: Validação mais flexível para detectar mais players
            // Se os offsets exatos não funcionaram, tentar pattern scanning como backup
            if (foundHP <= 0 || foundHP > 100 || (foundTeam != 2 && foundTeam != 3)) {
                // Reset e busca por pattern
                foundHP = 0;
                foundTeam = 0;

                for (int offset = 0; offset < 0x500; offset += 4) {
                    try {
                        int value = 0;
                        if (!IsBadReadPtr(reinterpret_cast<void*>(playerPawnPtr + offset), sizeof(int))) {
                            value = *reinterpret_cast<int*>(playerPawnPtr + offset);
                        }

                        if (foundHP == 0 && value > 0 && value <= 100) {
                            foundHP = value;
                        }
                        if (foundTeam == 0 && (value == 2 || value == 3)) {
                            foundTeam = value;
                        }
                        if (foundHP > 0 && foundTeam > 0) break;
                    } catch (...) {
                        continue;
                    }
                }
            }

            // FILTROS RIGOROSOS: Aceitar apenas players válidos
            if (foundHP > 0 && foundHP <= 100 && (foundTeam == 2 || foundTeam == 3)) {
                // Verificar se tem controller (players reais têm controller)
                uintptr_t controller = 0;
                try {
                    if (!IsBadReadPtr(reinterpret_cast<void*>(playerPawnPtr + 0x1A8), sizeof(uintptr_t))) {
                        controller = *reinterpret_cast<uintptr_t*>(playerPawnPtr + 0x1A8);
                    }
                } catch (...) {
                    continue; // Não é player se não conseguir ler controller
                }

                // Se não tem controller, não é player real
                if (!controller || controller < 0x10000) continue;

                // ACEITAR TODOS - mesmo com dados incompletos

                // Obter posição real do player com verificações
                Vector3 playerPos = {0, 0, 0};
                try {
                    uintptr_t sceneNodeAddr = playerPawnPtr + 0x328;
                    if (!IsBadReadPtr(reinterpret_cast<void*>(sceneNodeAddr), sizeof(uintptr_t))) {
                        uintptr_t sceneNode = *reinterpret_cast<uintptr_t*>(sceneNodeAddr);
                        if (sceneNode && sceneNode > 0x10000) {
                            uintptr_t posAddr = sceneNode + 0xD0;
                            if (!IsBadReadPtr(reinterpret_cast<void*>(posAddr), sizeof(Vector3))) {
                                playerPos = *reinterpret_cast<Vector3*>(posAddr);
                            }
                        }
                    }
                } catch (...) {
                    // Se falhar, usar posição zero
                }

                // Filtrar posições inválidas
                if (playerPos.x == 0 && playerPos.y == 0 && playerPos.z == 0) continue;
                if (abs(playerPos.x) > 3000 || abs(playerPos.y) > 3000 || abs(playerPos.z) > 1000) continue;

                // Calcular distância do local player
                float distance = GetDistance(localPos, playerPos);

                // Filtrar distâncias irreais
                if (distance > 2000.0f || distance < 10.0f) continue;

                // WorldToScreen com validação
                Vector2 screenPos;
                bool isOnScreen = WorldToScreen(playerPos, screenPos);

                // Filtrar posições de tela inválidas
                if (screenPos.x < -100 || screenPos.x > 2020 || screenPos.y < -100 || screenPos.y > 1180) continue;

                // CRIAR PlayerInfo apenas para players válidos
                PlayerInfo player;
                player.valid = true;
                player.health = foundHP;
                player.team = foundTeam;
                player.position = playerPos;
                player.name = "Player";
                player.onScreen = isOnScreen;
                player.screenPos = screenPos;
                player.distance = distance;

                // Adicionar player válido
                validEntities++;
                players.push_back(player);

                // Limitar a 10 players máximo (5v5 padrão)
                if (players.size() >= 10) break;
            }
        }

        // Debug removido para performance

    } catch (...) {
        std::cout << "[ERRO] Exceção em GetPlayers!" << std::endl;
    }

    return players;
}

PlayerInfo InternalMemory::GetPlayerInfo(uintptr_t entity) {
    PlayerInfo player;

    // Verificação inicial de segurança
    if (!entity || entity < 0x10000) return player;

    try {
        // Obter informações básicas com verificações
        player.entity = entity;

        int health = 0, team = 0;
        try {
            health = Read<int>(entity + Offsets::Netvars::BaseEntity::m_iHealth);
            team = Read<int>(entity + Offsets::Netvars::BaseEntity::m_iTeamNum);
        } catch (...) {
            return player; // Falha na leitura básica
        }

        player.health = health;
        player.team = team;

        if (player.health <= 0 || player.health > 100) return player;
        if (player.team != 2 && player.team != 3) return player; // Só T e CT

        // Verificar se tem scene node válido com verificação de segurança
        uintptr_t sceneNode = 0;
        try {
            sceneNode = Read<uintptr_t>(entity + Offsets::Netvars::BaseEntity::m_pGameSceneNode);
        } catch (...) {
            return player;
        }

        if (!sceneNode || sceneNode < 0x10000) return player;

        // Obter posição do scene node com verificação
        try {
            player.position = Read<Vector3>(sceneNode + Offsets::Netvars::GameSceneNode::m_vecAbsOrigin);
        } catch (...) {
            return player;
        }

        // Verificar se a posição é válida
        if (player.position.x == 0 && player.position.y == 0 && player.position.z == 0) return player;

        // Calcular distância com verificações
        uintptr_t localPawn = GetLocalPlayerPawn();
        if (localPawn && localPawn > 0x10000) {
            try {
                uintptr_t localSceneNode = Read<uintptr_t>(localPawn + Offsets::Netvars::BaseEntity::m_pGameSceneNode);
                if (localSceneNode && localSceneNode > 0x10000) {
                    Vector3 localPos = Read<Vector3>(localSceneNode + Offsets::Netvars::GameSceneNode::m_vecAbsOrigin);
                    player.distance = GetDistance(localPos, player.position);

                    // Verificar se a distância é razoável
                    if (player.distance > 5000.0f || player.distance < 1.0f) return player;
                }
            } catch (...) {
                // Se falhar no cálculo de distância, usar valor padrão
                player.distance = 100.0f;
            }
        }

        // Calcular head position com verificação
        try {
            Vector3 viewOffset = Read<Vector3>(entity + Offsets::Netvars::PlayerPawn::m_vecViewOffset);
            player.headPosition = player.position + viewOffset;
        } catch (...) {
            player.headPosition = player.position; // Usar posição base se falhar
        }

        // World to screen com verificação
        try {
            player.onScreen = WorldToScreen(player.position, player.screenPos);
            WorldToScreen(player.headPosition, player.headScreenPos);
        } catch (...) {
            player.onScreen = false;
        }

        player.valid = true;

    } catch (...) {
        player.valid = false;
    }

    return player;
}

bool InternalMemory::WorldToScreen(const Vector3& world, Vector2& screen) {
    try {
        Matrix4x4 matrix = GetViewMatrix();
        
        // CÁLCULO CORRETO DE WORLD-TO-SCREEN
        float w = matrix.m[3][0] * world.x + matrix.m[3][1] * world.y + matrix.m[3][2] * world.z + matrix.m[3][3];

        if (w < 0.001f) return false; // Atrás da câmera

        float x = matrix.m[0][0] * world.x + matrix.m[0][1] * world.y + matrix.m[0][2] * world.z + matrix.m[0][3];
        float y = matrix.m[1][0] * world.x + matrix.m[1][1] * world.y + matrix.m[1][2] * world.z + matrix.m[1][3];

        // Normalizar
        x /= w;
        y /= w;

        // Converter para coordenadas de tela (assumindo 1920x1080)
        screen.x = (1920.0f / 2.0f) * (1.0f + x);
        screen.y = (1080.0f / 2.0f) * (1.0f - y);

        // Debug desabilitado para reduzir spam
        // static int debugCount = 0;
        // if (debugCount % 100 == 0) {
        //     std::cout << "[W2S] World=(" << (int)world.x << "," << (int)world.y << "," << (int)world.z
        //               << ") Screen=(" << (int)screen.x << "," << (int)screen.y << ") w=" << w << std::endl;
        // }
        // debugCount++;

        // Verificar se está na tela
        bool onScreen = (screen.x >= 0 && screen.x <= 1920 && screen.y >= 0 && screen.y <= 1080);
        return onScreen;
        
    } catch (...) {
        return false;
    }
}

float InternalMemory::GetDistance(const Vector3& pos1, const Vector3& pos2) {
    Vector3 diff = pos1 - pos2;
    return diff.Length();
}

std::string InternalMemory::GetPlayerName(uintptr_t controller) {
    // Implementação simplificada - retorna placeholder
    return "Player";
}
