#pragma once
#include "pch.h"
#include <functional>

// Forward declarations
typedef HRESULT(__stdcall* Present_t)(IDXGISwapChain*, UINT, UINT);
typedef HRESULT(__stdcall* ResizeBuffers_t)(IDXGISwapChain*, UINT, UINT, UINT, DXGI_FORMAT, UINT);

class DirectXHook {
private:
    // DirectX objects
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGISwapChain* m_pSwapChain;
    ID3D11RenderTargetView* m_pRenderTargetView;

    // Hook data
    Present_t m_pOriginalPresent;
    ResizeBuffers_t m_pOriginalResizeBuffers;

    // Rendering
    std::function<void()> m_renderCallback;
    bool m_bInitialized;

    // Viewport info
    D3D11_VIEWPORT m_viewport;

    // Singleton instance for static callbacks
    static DirectXHook* s_pInstance;

public:
    DirectXHook();
    ~DirectXHook();

    bool Initialize();
    void Shutdown();

    // Callbacks
    void SetRenderCallback(std::function<void()> callback) { m_renderCallback = callback; }

    // Getters
    ID3D11Device* GetDevice() const { return m_pDevice; }
    ID3D11DeviceContext* GetContext() const { return m_pContext; }
    IDXGISwapChain* GetSwapChain() const { return m_pSwapChain; }
    D3D11_VIEWPORT GetViewport() const { return m_viewport; }

    // Rendering helpers
    void BeginRender();
    void EndRender();

private:
    bool HookDirectX();
    void UnhookDirectX();
    bool CreateRenderTarget();
    void CleanupRenderTarget();

    // Hook functions
    static HRESULT __stdcall PresentHook(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
    static HRESULT __stdcall ResizeBuffersHook(IDXGISwapChain* pSwapChain, UINT BufferCount, UINT Width, UINT Height, DXGI_FORMAT NewFormat, UINT SwapChainFlags);
};
