#pragma once

#include <Windows.h>
#include <d3d11.h>
#include <string>

// Forward declarations
struct ImGuiIO;

class Overlay {
private:
    // Window
    HWND overlayWindow;
    HWND targetWindow;
    WNDCLASSEXW windowClass;
    
    // DirectX 11
    ID3D11Device* device;
    ID3D11DeviceContext* deviceContext;
    IDXGISwapChain* swapChain;
    ID3D11RenderTargetView* renderTargetView;
    
    // Estado
    bool initialized;
    bool running;
    bool menuVisible;
    
    // Configurações
    int windowWidth;
    int windowHeight;
    
    // Funções privadas
    bool CreateOverlayWindow();
    bool InitializeDirectX();
    bool InitializeImGui();
    void CleanupDirectX();
    void CleanupImGui();
    void CleanupWindow();
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
    void UpdateWindowPosition();
    HWND FindTargetWindow();
    
public:
    Overlay();
    ~Overlay();
    
    bool Initialize();
    void Shutdown();
    
    void BeginFrame();
    void EndFrame();
    void RenderMenu();
    
    // Estado
    bool IsRunning() const { return running; }
    bool IsMenuVisible() const { return menuVisible; }
    void SetMenuVisible(bool visible) { menuVisible = visible; }
    void ToggleMenu() { menuVisible = !menuVisible; }
    
    // Getters
    HWND GetOverlayWindow() const { return overlayWindow; }
    HWND GetTargetWindow() const { return targetWindow; }
    ID3D11Device* GetDevice() const { return device; }
    ID3D11DeviceContext* GetDeviceContext() const { return deviceContext; }
    
    // Configurações de janela
    int GetWindowWidth() const { return windowWidth; }
    int GetWindowHeight() const { return windowHeight; }
    void SetWindowSize(int width, int height);
};
